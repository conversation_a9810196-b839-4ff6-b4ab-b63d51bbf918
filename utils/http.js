import { message } from 'ant-design-vue';
import axios from 'axios';
import debounce from 'lodash/debounce';

const http = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
  timeout: 60000,
});

const errortip = debounce(
  msg => {
    message.error(msg || '未知异常');
  },
  300,
  {
    maxWait: 1000,
  }
);

// 报错请求堆栈
let refreshing = false;
let queue = [];

/** 参数是否一致 */
let postStr = {};
const isRepeat = (url, data) => {
  let flag = true;
  const key = url + JSON.stringify(data);
  if (postStr[key]) {
    flag = false;
  } else {
    flag = true;
    postStr[key] = true;
  }
  return flag;
};

const getToken = token => {
  if (token) {
    return token.includes('Bearer') ? token : `Bearer ${token}`;
  } else {
    // if (window.location.href.includes('login')) {
    //   alert(1);
    //   // 最好是获取一下参数
    //   const params = getTargetUrlParams();
    //   console.log(params);
    //   // window.location.replace(`/#/login?${params}`);
    // } else {
    //   window.location.replace('/#/login');
    // }
  }
};
// 请求拦截器
http.interceptors.request.use(
  config => {
    const store = useStore();
    config.headers.Authorization = getToken(store.accessToken);
    if (window.localStorage.getItem('macAddr')) {
      config.headers['X-MAC-Addr'] = window.localStorage.getItem('macAddr');
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

const toLoginCode = [
  1001001001, 1001001002, 1001001003, 1001001005, 1001001006, 1001001007,
  1002002002, 1002002009, 1002002012, 1002002013, 1004, 1003,
];

async function refreshToken() {
  try {
    const store = useStore();
    if (!store.refreshToken) {
      return { code: -1 };
    } else {
      const res = await http.post('/admin/auth/refresh-token', {
        refreshToken: store.refreshToken,
      });
      if (res.code == 0) {
        store.setToken(res.data);
      }
      return res;
    }
  } catch (error) {
    console.error('refresh-token:', error);
    // 该接口异常，跳转到登录的逻辑
    return { code: -1 };
  }
}
// 响应拦截器
http.interceptors.response.use(
  async response => {
    const store = useStore();
    const { data, config, status } = response;
    if (config.method == 'post') {
      // 计算当前URL传递的参数
      const key = config.url + config.data;
      if (postStr[key]) {
        postStr[key] = null;
      }
    }

    if (config?.responseType == 'arraybuffer') {
      return data;
    }
    if (status == 200 && data.code == 0) {
      return data;
    } else if (status == 200 && data.code == 1002002014) {
      message.error(data?.message);
      if (window.location.href.indexOf('/#/login')) {
        // 本身在登录页(不跳转到没有权限页面)
        return data;
      } else {
        window.location.replace('/#/no-auth');
      }
    } else if (data.code === 401) {
      if (store.refreshToken) {
        // 不存在刷新token，跳转到登录页
        queue = [];
        store.clearUser();
        window.localStorage.clear();
        message.error(data.message);
        window.location.replace('/#/login');
        return data;
      }

      // 刷新token
      if (refreshing && !config.url.includes('/refresh-token')) {
        return new Promise(resolve => {
          queue.push({
            config,
            resolve,
          });
        });
      }

      if (!config.url.includes('/refresh-token')) {
        refreshing = true;
        const res = await refreshToken();
        refreshing = false;

        if (res.code === 0) {
          queue.forEach(({ config, resolve }) => {
            resolve(http(config));
          });
          queue = [];
          return http(config);
        } else {
          // 续签token失败，跳转到登录页
          queue = [];
          store.clearUser();
          window.localStorage.clear();
          message.error(data.message);
          window.location.replace('/#/login');
          return data;
        }
      } else {
        return data;
      }
    } else if (toLoginCode.includes(data.code)) {
      store.clearUser();
      window.localStorage.clear();
      message.error(data.message);
      window.location.replace('/#/login');
      return data;
    } else {
      // debugger
      errortip(data?.message);
      return Promise.reject(data);
    }
  },
  error => {
    // $ 清除缓存的参数
    if (error.config.method == 'post') {
      // 计算当前URL传递的参数
      const key = error.config.url + error.config.data;
      if (postStr[key]) {
        postStr[key] = null;
      }
    }
    return Promise.reject(error);
  }
);
const get = (url, params = {}) => {
  return http({
    method: 'get',
    url: url,
    params,
  });
};

const post = (
  url,
  data,
  params,
  headers = { 'Content-Type': 'application/json' }
) => {
  const flag = isRepeat(url, data);
  if (flag) {
    return http({
      method: 'post',
      url: url,
      headers,
      data,
      params,
    });
  } else {
    return Promise.reject('参数一样');
  }
};

const importPost = (
  url,
  data,
  headers = { 'Content-Type': 'multipart/form-data' }
) => {
  return http({
    method: 'post',
    url: url,
    headers,
    data,
  });
};

const download = (
  url,
  data,
  name,
  fn,
  type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
) => {
  return http({
    url,
    method: 'post',
    data,
    responseType: 'arraybuffer',
  }).then(blob => {
    try {
      // $ 当下载excel文件正常时content-type类型application/vnd.ms-excel;charset=utf-8 不正常时才是application/json
      // ArrayBuffer需要将其转换为字符串来检查内容
      const data = new TextDecoder('utf-8').decode(blob);
      // 如果类型为application/json表示不是excel文件，解析成功，否则报错
      const jsonData = JSON.parse(data);
      message.error(jsonData.message);
      return false;
    } catch (error) {
      console.error('error: ', error);
      const url = window.URL.createObjectURL(
        new Blob([blob], {
          type,
        })
      );
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', `${name}`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      fn && fn();
      setTimeout(() => window.URL.revokeObjectURL(url), 1000);
      return true;
    }
  });
};
const form = (url, data) => {
  let fd = new FormData();
  for (let key in data) {
    fd.append(key, data[key]);
  }
  return http({
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    url: url,
    data: fd,
  });
};

const put = (url, data) => {
  return http({
    method: 'put',
    url: url,
    data,
  });
};

const getDownload = (
  url,
  params,
  name,
  type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
) => {
  return http({
    url,
    params,
    method: 'get',
    responseType: 'arraybuffer',
    headers: { 'Content-Type': 'application/json; application/octet-stream' },
  }).then(res => {
    try {
      const blobs = new Blob([res.data], {
        type,
      });
      const url = window.URL.createObjectURL(blobs);
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', `${name}`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setTimeout(() => window.URL.revokeObjectURL(url), 1000);
    } catch (error) {
      console.error('error: ', error);
    }
  });
};

export default { get, post, download, form, put, getDownload, importPost };
export { http };
