import { createRouter, createWebHashHistory } from 'vue-router';
import config from './config';

const init = async () => {
  // 初始化时只加载基础路由，动态路由在路由守卫中按需加载
  let routes = [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/components/login/index.vue'),
    },
    {
      path: '/:pathMatch(.*)',
      redirect: '/404',
    },
  ];
  return Promise.resolve(routes);
};
const asRouter = async () => {
  return new Promise(resolve => {
    init().then(res => {
      const router = createRouter({
        history: createWebHashHistory(),
        scrollBehavior() {
          return { top: 0, left: 0 };
        },
        routes: res,
      });
      config(router);
      return resolve(router);
    });
  });
};

export default asRouter;
