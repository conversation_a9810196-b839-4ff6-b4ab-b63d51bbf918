import getRoute from './getRoute';

export default router => {
  router.beforeEach(async (to, _from, next) => {
    const store = useStore();

    // 如果是登录页面，直接放行
    if (to.path === '/login') {
      next();
      return;
    }

    // 如果用户已登录但动态路由还没有加载
    if (store.accessToken && !router.hasRoute('main')) {
      try {
        // 重新获取动态路由
        const [routeArr] = await getRoute();

        // 添加主布局路由和子路由
        const mainRoute = {
          component: () => import('@/components/layout/index.vue'),
          name: 'main',
          children: [
            ...routeArr,
            {
              path: '/404',
              name: '404',
              component: () => import('../../../../components/common/404.vue'),
            },
            {
              path: '/no-auth',
              name: 'no-auth',
              component: () => import('../../../../components/common/NoAuth.vue'),
            },
            {
              path: '/redirect',
              name: 'redirect',
              component: () => import('../../../../components/common/redirect.vue'),
            },
          ],
        };

        // 添加根路径重定向
        router.addRoute({
          path: '',
          redirect: '/home',
        });

        // 添加主路由
        router.addRoute(mainRoute);

        // 重新导航到目标路由
        next({ ...to, replace: true });
        return;
      } catch (error) {
        console.error('动态路由加载失败:', error);
        // 如果加载失败，跳转到登录页
        next('/login');
        return;
      }
    }

    // 如果没有登录且不是登录页，跳转到登录页
    if (!store.accessToken) {
      next('/login');
      return;
    }

    next();
  });

  router.afterEach(() => {
    setTimeout(() => {}, 300);
  });
};
