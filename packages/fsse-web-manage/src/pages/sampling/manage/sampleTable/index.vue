<template>
  <div class="indicatorPage">
    <div class="indicatorHead">
      <div class="backArrow" @click="backClick"><ArrowLeftOutlined /></div>
      <div class="indicatorTitle">查看样本</div>
    </div>
    <div class="indicatorBody">
      <div class="bodyLeftBox">
        <div
          class="leftItemBox"
          v-for="(item, index) in state.leftList"
          :key="item.id"
          @click="handleLeftItemClick(item)"
        >
          <div class="numSerial">
            <div class="numSerial_index">{{ index + 1 }}</div>
          </div>
          <div
            :class="{
              item_title: true,
              item_title_active: moduleActive === item.value,
            }"
          >
            <div class="item_titleText">{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div class="bodyRightBox">
        <KeepAlive>
          <component :is="moduleList[moduleActive]"></component>
        </KeepAlive>
      </div>
    </div>
  </div>
</template>

<script setup name="indicator">
// 省 查看所有
// 市查看leftList   样本区/县; 样本校; 样本学生; 样本教师; 样本家长;
// 区/县查看leftList  样本校;样本学生;样本教师;样本家长;
// 学校 查看leftList 样本学生;样本教师;样本家长;
import { theme } from 'ant-design-vue';
//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
const { token } = theme.useToken();

const mainStore = useStore();
const level = mainStore?.userInfo.fsseOrg.level;
console.log('level等级', level);
const leftListMap = {
  1: [
    {
      id: '1',
      name: '样本市',
      value: 'Downtown',
    },
    {
      id: '2',
      name: '样本区/县',
      value: 'County',
    },
    {
      id: '3',
      name: '样本学校',
      value: 'School',
    },
    {
      id: '4',
      name: '样本学生',
      value: 'Pupil',
    },
    // {
    //   id: '5',
    //   name: '样本家长',
    //   value: 'Patriarch',
    // },
    {
      id: '6',
      name: '样本教师',
      value: 'Teacher',
    },
  ],
  2: [
    {
      id: '2',
      name: '样本区/县',
      value: 'County',
    },
    {
      id: '3',
      name: '样本学校',
      value: 'School',
    },
    {
      id: '4',
      name: '样本学生',
      value: 'Pupil',
    },
    // {
    //   id: '5',
    //   name: '样本家长',
    //   value: 'Patriarch',
    // },
    {
      id: '6',
      name: '样本教师',
      value: 'Teacher',
    },
  ],
  3: [
    {
      id: '3',
      name: '样本学校',
      value: 'School',
    },
    {
      id: '4',
      name: '样本学生',
      value: 'Pupil',
    },
    // {
    //   id: '5',
    //   name: '样本家长',
    //   value: 'Patriarch',
    // },
    {
      id: '6',
      name: '样本教师',
      value: 'Teacher',
    },
  ],
  4: [
    {
      id: '4',
      name: '样本学生',
      value: 'Pupil',
    },
    // {
    //   id: '5',
    //   name: '样本家长',
    //   value: 'Patriarch',
    // },
    {
      id: '6',
      name: '样本教师',
      value: 'Teacher',
    },
  ],
};

const state = ref({
  leftList: leftListMap[level],
});

const moduleActive = ref(state.value.leftList[0].value);

const moduleList = {
  Downtown: defineAsyncComponent(() => import('./module/downtown.vue')), // 市
  County: defineAsyncComponent(() => import('./module/county.vue')), // 区/县
  School: defineAsyncComponent(() => import('./module/school.vue')), // 学校
  Pupil: defineAsyncComponent(() => import('./module/pupil.vue')), // 学生
  Patriarch: defineAsyncComponent(() => import('./module/patriarch.vue')), // 家长
  Teacher: defineAsyncComponent(() => import('./module/teacher.vue')), // 教师
};

const handleLeftItemClick = item => {
  moduleActive.value = item.value;
};

// 返回上一页
const backClick = () => {
  history.back();
};
</script>

<style lang="less" scoped>
.indicatorPage {
  height: 100%;
  width: 100%;
  .indicatorHead {
    height: 54px;
    border-bottom: 1px solid #ecedf1;
    padding-left: 16px;
    padding-right: 16px;
    display: flex;
    align-items: center;
    .backArrow {

      color: var(--primary-color);
      cursor: pointer;
    }
    .indicatorTitle {
      padding-left: 10px;
      font-weight: 600;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }
}

.indicatorBody {
  display: flex;
  height: calc(100% - 54px);
  .bodyLeftBox {
    width: 165px;
    min-width: 165px;
    border-right: 1px solid #ecedf1;
    padding: 16px 8px 16px 16px;
    overflow-x: hidden;
  }
  .bodyRightBox {
    flex: 1;
    padding: 16px;
    overflow-x: hidden;
  }
}

.leftItemBox {
  display: flex;
  align-items: center;
  padding-bottom: 14px;
  .numSerial {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    .numSerial_index {
      font-weight: 600;
      font-size: 14px;
      color: var(--primary-color);
      line-height: 20px;
      text-align: right;
      font-style: normal;
    }
  }
  .item_title {
    flex: 1;
    white-space: nowrap;
    font-weight: 400;
    background: #ffffff;
    border-radius: 4px;
    padding: 6px 8px;
    margin-left: 5px;
    color: #262626;
    cursor: pointer;
    .item_titleText {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      text-align: left;
      font-style: normal;
    }
  }
  .item_title_active {
    background: v-bind('token.colorPrimaryBg');
    color: var(--primary-color);
  }
}
</style>
