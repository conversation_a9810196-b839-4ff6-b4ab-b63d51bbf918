<!-- 样本学生 -->
<template>
  <div>
    <div pb16>
      <searchForm
        v-model:formState="query"
        :formList="formList"
        @submit="searchBtn"
        @reset="resetBtn"
      >
      </searchForm>
    </div>
    <div
      style="
        font-weight: 400;
        font-size: 14px;
        color: #595959;
        padding-bottom: 12px;
      "
    >
      <span>抽样人：{{ state.info.student.samplingMemberName || '-' }}</span>
      <span ml-32>抽样时间：{{ state.info.student.createTime || '-' }}</span>
    </div>
    <ETable
      hash="downtown-table"
      :loading="page.loading"
      :columns="columns"
      :dataSource="computedList"
      :total="page.total"
      @paginationChange="paginationChange"
      :current="query.pageNo"
    >
    <template #emptyText-slot>
        <div class="empty_box">
          <img src="@/assets/images/jc-empty.png" alt="jc-empty.png" />
          <div>样本学生仅在考前2小时可查看,如有疑问请联系管理员</div>
        </div>
      </template>
    </ETable>
  </div>
</template>

<script setup name="downtown">
const route = useRoute();
const formList = [
  {
    type: 'input',
    value: 'name',
    label: '学生姓名',
  },
];
// const columns = ref([
//   { title: '地市名称', dataIndex: 'cityName', key: 'cityName' },
//   { title: '区/县名称', dataIndex: 'districtName', key: 'districtName' },
//   { title: '学校名称', dataIndex: 'schoolName', key: 'schoolName' },
//   { title: '学生姓名', dataIndex: 'name', key: 'name' },
//   { title: '所在年级', dataIndex: 'grade', key: 'grade' },
//   { title: '班级', dataIndex: 'classesName', key: 'classesName' },
// ]);

const columns = ref([
  { title: '地市名称', dataIndex: 'cityName', key: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName', key: 'districtName' },
  { title: '学校名称', dataIndex: 'schoolName', key: 'schoolName' },
  { title: '年级', dataIndex: 'grade', key: 'grade' },
  { title: '现所在班级', dataIndex: 'classesName', key: 'classesName' },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '性别', dataIndex: 'gender', key: 'gender' },
  { title: '学籍号', dataIndex: 'studentCode', key: 'studentCode' },
  { title: '出生日期', dataIndex: 'birthday', key: 'birthday' },
  { title: '备注', dataIndex: 'remark', key: 'remark' },

]);

const state = ref({
  fixedObj: {
    collectJobId: route.query.collectJobId,
    samplingJobId: '',
  },
  info: {
    student: {
      samplingMemberName: '',
      createTime: '',
    },
  },
});
let { query, page, getList, reset, paginationChange } = useList(
  '/manage/collect/job/student/page',
  state.value.fixedObj
);

const computedList = computed(() => {
  // 拼接表格数据
  let resultArr = page.list.reduce((result, item) => {
    if (item.indicators && item.indicators.length) {
      let newItem = { ...item };
      item.indicators.forEach(indicator => {
        newItem[indicator.indicatorId] = indicator.indicatorRuleValue;
      });
      result.push(newItem);
    } else {
      result.push(item);
    }
    return result;
  }, []);

  return resultArr;
});

const searchBtn = () => {
  getList(state.value.fixedObj);
};
const resetBtn = () => {
  reset(state.value.fixedObj);
};

// 获取指标的动态表头
const getColumns = async () => {
  try {
    const res = await http.post('/manage/collect/job/listCollectJobIndicator', {
      collectJobId: route.query.collectJobId,
      indicatorType: 'student',
    });

    // 过滤出 checked 字段为 true的
    const checkedArr = res.data.filter(item => item.checked === true);
    const columnsArr = checkedArr.map(item => {
      return {
        title: item.name,
        dataIndex: item.id,
        key: item.id,
      };
    });
    columns.value = [...columns.value, ...columnsArr];
  } catch (e) {
    console.warn(e);
  }
};

const getsamplingJobId = async () => {
  try {
    const res = await http.post(`/manage/sampling/job/getDetails`, {
      id: route.query.samplingJobId,
    });
    // 获取抽样任务id
    state.value.fixedObj.samplingJobId = res.data?.id;
    // 获取抽样人 时间
    if (res.data.samplingJobDetails) {
      res.data.samplingJobDetails.forEach(
        item => (state.value.info[item.samplingDataType] = item)
      );
    }
    if(res.data?.id){
      await getList(state.value.fixedObj);
    }
  } catch (e) {
    console.warn(e);
  }
};

onMounted(() => {
  getColumns();
  getsamplingJobId();
});
</script>

<style lang="less" scoped></style>
