<!-- 采集管理里的我参与的 -->
<template>
  <div class="participatePage">
    <div pb16>
      <searchForm
        v-model:formState="query"
        :formList="formList"
        @submit="searchBtn"
        @reset="resetBtn"
      >
      </searchForm>
    </div>
    <div class="participateContent">
      <div class="participateListBox" v-if="page.list && page.list.length">
        <div
          class="participateItem"
          v-for="(item, index) in page.list"
          :key="index"
        >
          <div class="status" :style="{ background: statusBg[item.jobStatus] }">
            {{ statusText[item.jobStatus] }}
          </div>
          <div class="item_left">
            <div class="imgBox">
              <svg-icon name="icon-mbgl-mr" size="38"></svg-icon>
            </div>
            <div class="left_titleBox">
              <div class="left_titleBoxName">
                {{ item.versionName }}
              </div>
              <div class="left_titleBoxTime">
                {{ item.orgName }}发布于{{ item.createTime }}
              </div>
            </div>
          </div>
          <div class="item_center">
            <a-progress
              :showInfo="false"
              :size="14"
              :percent="item.jobProgress || 0"
              :strokeColor="token.colorPrimaryBorder"
              :trailColor="token.colorPrimaryBg"
            />
            <div class="item_center_bottom">
              <div class="progressTime">任务截止时间：{{ item.endTime }}</div>
              <div class="progressNum">
                已采集：<span class="progressNum_text"
                  >{{ item.jobProgress || 0 }}%</span
                >
              </div>
            </div>
          </div>
          <div class="item_right">
            <div class="btn" @click="taskDetails(item)">
              <i class="iconfont icon-bggl-scbg"></i> 任务详情
            </div>
          </div>
        </div>
      </div>

      <!-- 分页器 -->
      <div
        style="margin: 16px 16px 0 0; text-align: right"
        v-if="page.list && page.list.length"
      >
        <Pagination
          :total="page.total"
          @paginationChange="paginationChange"
          :current="query.pageNo"
        ></Pagination>
      </div>

      <div v-else>
        <div
          :style="{ height: 'calc(100vh - 230px)' }"
          flex
          flex-col
          items-center
          justify-center
        >
          <img
            width="180"
            height="180"
            src="@/assets/images/empty.png"
            alt="暂无数据"
          />
          <div style="color: rgba(0, 0, 0, 0.65); font-size: 14px">
            暂无数据
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="gather-participate">
const router = useRouter();
import { theme } from 'ant-design-vue';
//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
const { token } = theme.useToken();
let { query, page, getList, reset, paginationChange } = useList(
  '/manage/collect/job/page',
  { queryType: 2 }
);

const statusBg = {
  pending: '#A1A1A1',
  in_progress: token.value.colorPrimary,
  finished: '#D4D4D4',
};
const statusText = {
  pending: '未开始',
  in_progress: '进行中',
  finished: '已结束',
};
const formList = [
  {
    type: 'input',
    value: 'versionName',
    label: '采集版本名称',
  },
  {
    type: 'select',
    value: 'jobStatus',
    label: '状态',
    list: [
      {
        label: '全部',
        value: null,
      },
      {
        label: '未开始',
        value: 'pending',
      },
      {
        label: '进行中',
        value: 'in_progress',
      },
      {
        label: '已结束',
        value: 'finished',
      },
    ],
  },
];

const searchBtn = () => {
  getList({ queryType: 2 });
};
const resetBtn = () => {
  reset({ queryType: 2 });
};

const taskDetails = item => {
  console.log('item', item);
  router.push({
    path: '/gather/taskDetails',
    query: {
      collectJobId: item.id,
    },
  });
};

function is1Array(arr) {
  return Array.isArray(arr) && arr.length === 1;
}

/**
 * 通用的获取URL参数函数
 * 支持hash模式和history模式的URL参数解析
 * @param {string} url - 要解析的URL，如果不传则使用当前页面URL
 * @param {string} paramName - 要获取的参数名，如果不传则返回所有参数对象
 * @returns {string|object|null} 返回参数值、参数对象或null
 */
function getUrlParams(url = window.location.href, paramName = null) {
  try {
    const urlObj = new URL(url);
    const params = {};

    // 解析query参数 (?后面的参数)
    urlObj.searchParams.forEach((value, key) => {
      params[key] = decodeURIComponent(value);
    });

    // 解析hash中的参数
    const hash = urlObj.hash;
    if (hash) {
      // 处理hash路由中的参数，支持多种格式：
      // 1. #/path?param=value
      // 2. #path?param=value
      // 3. #?param=value
      // 4. #param=value&param2=value2

      let hashParams = '';

      // 检查hash中是否包含?
      if (hash.includes('?')) {
        hashParams = hash.split('?')[1];
      } else {
        // 如果hash不包含?，但包含=，则整个hash可能就是参数
        if (hash.includes('=') && !hash.includes('/')) {
          hashParams = hash.substring(1); // 去掉#号
        }
      }

      // 解析hash中的参数
      if (hashParams) {
        // 移除可能的fragment部分（#后面的部分）
        hashParams = hashParams.split('#')[0];

        const hashSearchParams = new URLSearchParams(hashParams);
        hashSearchParams.forEach((value, key) => {
          // hash参数优先级更高，会覆盖query参数
          params[key] = decodeURIComponent(value);
        });
      }
    }

    // 如果指定了参数名，返回对应的值
    if (paramName) {
      return params.hasOwnProperty(paramName) ? params[paramName] : null;
    }

    // 返回所有参数对象
    return params;
  } catch (error) {
    console.error('解析URL参数时发生错误:', error);
    return paramName ? null : {};
  }
}

// 获取路由参数

onMounted(async () => {
  await getList();
  const urlObj = getUrlParams();
  // 如果来源是官网的话 并且只有一条数据的话 进行代码进详情去
  if (urlObj.source === 'os' && is1Array(page.list)) {
    taskDetails(page.list[0]);
  }
});
</script>

<style lang="less" scoped>
.participatePage {
  padding: 20px;
}

.participateListBox {
  .participateItem {
    height: 84px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 16px 46px 16px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
    .status {
      position: absolute;
      top: 5px;
      right: -20px;
      font-weight: 500;
      font-size: 11px;
      color: #ffffff;
      height: 20px;
      line-height: 20px;
      width: 76px;
      text-align: center;
      transform: rotate(40deg);
    }

    .item_left {
      display: flex;
      align-items: center;
      width: 500px;
      .imgBox {
        width: 50px;
        height: 50px;
        background-color: v-bind('token.colorPrimaryBg');
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .left_titleBox {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 50px;
        padding-left: 12px;

        .left_titleBoxName {
          font-weight: 500;
          font-size: 14px;
          color: #000000;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
        .left_titleBoxTime {
          font-weight: 400;
          font-size: 14px;
          color: #595959;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
      }
    }
    .item_center {
      min-width: 400px;
      .item_center_bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .progressTime {
          font-weight: 400;
          font-size: 14px;
          color: #595959;
          line-height: 20px;
          text-align: left;
          font-style: normal;
        }
        .progressNum {
          font-weight: 400;
          font-size: 14px;
          color: #595959;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          .progressNum_text {
            color: var(--primary-color);
          }
        }
      }
    }
    .item_right {
      .btn {
        padding-left: 20px;
        color: #262626;
        font-size: 14px;
        cursor: pointer;
        &:hover {
          color: var(--primary-color);
        }
      }
    }
  }
}
</style>
