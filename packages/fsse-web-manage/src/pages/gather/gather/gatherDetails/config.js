// 市
const cityColumns = [
  { title: '省名称', dataIndex: 'provinceName' },
  { title: '地市名称', dataIndex: 'name' },
  {
    title: '状态',
    dataIndex: 'collectStatus',
    customRender: ({ text }) => (text == '0' ? '未上报' : '已上报'),
  },
  { title: '上报人', dataIndex: 'reportUserName' },
  { title: '上报时间', dataIndex: 'reportTime' },
];

// 区/县
const districtColumns = [
  { title: '省名称', dataIndex: 'provinceName' },
  { title: '地市名称', dataIndex: 'cityName' },
  { title: '区/县名称', dataIndex: 'name' },
  {
    title: '状态',
    dataIndex: 'collectStatus',
    customRender: ({ text }) => (text == '0' ? '未上报' : '已上报'),
  },
  { title: '上报人', dataIndex: 'reportUserName' },
  { title: '上报时间', dataIndex: 'reportTime' },
];

// 学校
const schoolColumns = [
  { title: '地市名称', dataIndex: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName' },
  { title: '学校名称', dataIndex: 'name' },
  { title: '学校简称', dataIndex: 'shortName' },
  {
    title: '状态',
    dataIndex: 'collectStatus',
    customRender: ({ text }) => (text == '0' ? '未上报' : '已上报'),
  },
  { title: '上报人', dataIndex: 'reportUserName' },
  { title: '上报时间', dataIndex: 'reportTime' },
];

// 学生
// const studentColumns = [
//   { title: '地市名称', dataIndex: 'cityName' },
//   { title: '区/县名称', dataIndex: 'districtName' },
//   { title: '学校名称', dataIndex: 'schoolName' },
//   { title: '学生姓名', dataIndex: 'name', key: 'name' },
//   { title: '所在年级', dataIndex: 'grade', key: 'grade' },
//   { title: '班级', dataIndex: 'classesName', key: 'classesName' },
//   { title: '年龄（岁）', dataIndex: 'age', key: 'age' },
//   { title: '学籍号', dataIndex: 'studentCode', key: 'studentCode' },
// ];


const studentColumns = [
  { title: '地市名称', dataIndex: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName' },
  { title: '学校名称', dataIndex: 'schoolName' },
  { title: '年级', dataIndex: 'grade', key: 'grade' },
  { title: '现所在班级', dataIndex: 'classesName', key: 'classesName' },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '性别', dataIndex: 'gender', key: 'gender' },
  { title: '学籍号', dataIndex: 'studentCode', key: 'studentCode' },
  { title: '出生日期', dataIndex: 'birthday', key: 'birthday' },
  { title: '备注', dataIndex: 'remark', key: 'remark' },
];


const relationsObj = {
  1: '父亲',
  2: '母亲',
  3: '爷爷',
  4: '奶奶',
  5: '外公',
  6: '外婆',
  7: '其他',
  default: '',
};

function getrelations(key) {
  return relationsObj[key] || relationsObj.default;
}

// 家长
const parentColumns = [
  { title: '地市名称', dataIndex: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName' },
  { title: '学校名称', dataIndex: 'schoolName' },
  { title: '学生姓名', dataIndex: 'studentName', key: 'studentName' },
  { title: '学生学籍号', dataIndex: 'studentCode', key: 'studentCode' },
  { title: '所在年级', dataIndex: 'grade', key: 'grade' },
  { title: '班级', dataIndex: 'classesName', key: 'classesName' },
  { title: '家长姓名', dataIndex: 'name', key: 'name' },
  { title: '家长电话', dataIndex: 'phone', key: 'phone' },
  {
    title: '与学生关系',
    dataIndex: 'relations',
    key: 'relations',
    customRender: ({ text }) => getrelations(text),
  },
];

// 教师
// const teacherColumns = [
//   { title: '地市名称', dataIndex: 'cityName' },
//   { title: '区/县名称', dataIndex: 'districtName' },
//   { title: '学校名称', dataIndex: 'schoolName' },
//   { title: '教师姓名', dataIndex: 'name', key: 'name' },
//   { title: '手机号', dataIndex: 'phone', key: 'phone' },
//   { title: '出生年月', dataIndex: 'birthday', key: 'birthday' },
//   { title: '上学期任教科目', dataIndex: 'subjectName', key: 'subjectName' },
//   { title: '所属学段', dataIndex: 'section', key: 'section' },
//   {
//     title: '上学期是否为班主任',
//     dataIndex: 'isLeader',
//     key: 'isLeader',
//     customRender: ({ text }) => (text === false ? '否' : '是'),
//   },
//   {
//     title: '是否为管理干部',
//     dataIndex: 'isManager',
//     key: 'isManager',
//     customRender: ({ text }) => (text === false ? '否' : '是'),
//   },
//   {
//     title: '是否为校长',
//     dataIndex: 'isPrincipal',
//     key: 'isPrincipal',
//     customRender: ({ text }) => (text === false ? '否' : '是'),
//   },
// ];

const teacherColumns = [
  { title: '地市名称', dataIndex: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName' },
  { title: '学校名称', dataIndex: 'schoolName' },
  { title: '教师姓名', dataIndex: 'name', key: 'name' },
  { title: '手机号', dataIndex: 'phone', key: 'phone' },
  { title: '出生日期', dataIndex: 'birthday', key: 'birthday' },
  { title: '任教年级', dataIndex: 'gradeName', key: 'gradeName' },
  { title: '任教科目', dataIndex: 'subjectName', key: 'subjectName' },
  { title: '所属学段', dataIndex: 'section', key: 'section' },
  {
    title: '是否为班主任',
    dataIndex: 'isLeader',
    key: 'isLeader',
    customRender: ({ text }) => (text === false ? '否' : '是'),
  },
  {
    title: '是否为校长',
    dataIndex: 'isPrincipal',
    key: 'isPrincipal',
    customRender: ({ text }) => (text === false ? '否' : '是'),
  },
  { title: '备注', dataIndex: 'remark', key: 'remark' },
];


// 监测室
// const testingRoomColumns = [
//   { title: '地市名称', dataIndex: 'cityName' },
//   { title: '区/县名称', dataIndex: 'districtName' },
//   { title: '学校名称', dataIndex: 'schoolName' },
//   { title: '监测教室', dataIndex: 'name', key: 'name' },
//   { title: '计算机台数', dataIndex: 'computerNumber', key: 'computerNumber' },
//   {
//     title: '是否配备耳机',
//     dataIndex: 'provideHeadphone',
//     key: 'provideHeadphone',
//   },
//   { title: '电脑配置信息', dataIndex: 'configuration', key: 'configuration' },
//   { title: '座位布局图', dataIndex: 'layout', key: 'layout' },

//   { title: '行*列', dataIndex: 'row', key: 'row' },
// ];

const testingRoomColumns = [
  { title: '地市名称', dataIndex: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName' },
  { title: '学校名称', dataIndex: 'schoolName' },
  { title: '监测教室', dataIndex: 'name', key: 'name' },
  { title: '计算机台数', dataIndex: 'computerNumber', key: 'computerNumber' },
  // {
  //   title: '是否配备耳机',
  //   dataIndex: 'provideHeadphone',
  //   key: 'provideHeadphone',
  // },
  // { title: '电脑配置信息', dataIndex: 'configuration', key: 'configuration' },
  // { title: '座位布局图', dataIndex: 'layout', key: 'layout' },

  // { title: '行*列', dataIndex: 'row', key: 'row' },
];



const person_type = {
  1: '监测点主任',
  2: '监测点副主任',
  3: '信息员',
  4: '安保人员',
  5: '司时员',
  6: '计算机技术人员',
  7: '主监测员',
  8: '副监测员',
  9: '学校负责人',
  default: '',
};
function getPersonType(key) {
  return person_type[key] || person_type.default;
}

// 监测员
// const monitorColumns = [
//   { title: '地市名称', dataIndex: 'cityName' },
//   { title: '区/县名称', dataIndex: 'districtName' },
//   { title: '学校名称', dataIndex: 'schoolName' },
//   { title: '监测员姓名', dataIndex: 'name', key: 'name' },
//   { title: '联系方式', dataIndex: 'phone', key: 'phone' },
//   { title: '人员类型', dataIndex: 'type', key: 'type', customRender: ({ text }) => getPersonType(text)},
//   { title: '监测年级', dataIndex: 'grade', key: 'grade' },
// ];

const monitorColumns = [
  { title: '地市名称', dataIndex: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName' },
  { title: '学校名称', dataIndex: 'schoolName' },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '手机号', dataIndex: 'phone', key: 'phone' },
  { title: '人员类型', dataIndex: 'type', key: 'type', customRender: ({ text }) => getPersonType(text)},
  { title: '监测年级', dataIndex: 'grade', key: 'grade' },
];

// 表格列表
export const columnsMapType = {
  city: cityColumns,
  district: districtColumns,
  school: schoolColumns,
  student: studentColumns,
  // eltern: parentColumns,
  teacher: teacherColumns,
  monitoring_room: testingRoomColumns,
  monitoring_member: monitorColumns,
};
