<!-- 采集系统任务详情主入口 -->

<!-- 4个级别的机构都有不同的信息上报界面 就是有4个组件 -->

<!-- 注意样式就行了 -->
<template>
  <div class="taskDetailsPage">
    <div class="taskDetailsHead">
      <div class="backArrow" @click="backClick"><ArrowLeftOutlined /></div>
      <div class="taskDetailsTitle">任务详情</div>
    </div>
    <div class="taskDetailsBody">
      <!-- 省级 -->
      <ProvinceRank v-if="level === 1"></ProvinceRank>
      <!-- 市级 -->
      <DowntownRank v-if="level === 2" indicatorType="city"></DowntownRank>
      <!-- 县区级别 -->
      <DowntownRank v-if="level === 3" indicatorType="district"></DowntownRank>
      <!-- 校级 -->
      <SchoolRank v-if="level === 4"></SchoolRank>
    </div>
  </div>
</template>

<script setup name="taskDetails">
import ProvinceRank from '@/pages/manage/manage/joinTask/child/infoSend/provinceRank.vue';
import DowntownRank from '@/pages/manage/manage/joinTask/child/infoSend/downtownRank.vue';
import SchoolRank from '@/pages/manage/manage/joinTask/child/infoSend/schoolRank.vue';

const mainStore = useStore();
let level = mainStore?.userInfo.fsseOrg.level;

const router = useRouter();
const backClick = () => {
  router.push({
    path: '/gather/gather',
  });
};
</script>

<style lang="less" scoped>
.taskDetailsPage {
  display: flex;
  flex-direction: column;
  height: 100%;
  .taskDetailsHead {
    height: 54px;
    border-bottom: 1px solid #ecedf1;
    padding-left: 16px;
    padding-right: 16px;
    display: flex;
    align-items: center;
    .backArrow {
      color: var(--primary-color);
      cursor: pointer;
    }
    .taskDetailsTitle {
      padding-left: 10px;
      font-weight: 600;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }
}

.stepsBox {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 24px;
  padding-bottom: 24px;
}

.roundBox {
  display: flex;
  align-items: center;
  cursor: pointer;
  .roundBox_item {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid #d9d9d9;
    display: flex;
    align-items: center;
    justify-content: center;
    .roundBox_item_num {
      font-weight: 600;
      font-size: 14px;
      color: #595959;
      line-height: 24px;
      text-align: left;
      font-style: normal;
    }
  }
  .roundBox_item_active {
    border: 1px solid var(--primary-color);
    .roundBox_item_num {
      color: var(--primary-color);
    }
  }
  .roundBox_text {
    font-weight: 600;
    font-size: 14px;
    color: #8c8c8c;
    line-height: 24px;
    text-align: left;
    font-style: normal;
    padding-left: 12px;
  }
  .roundBox_text_active {
    color: var(--primary-color);
  }
}

.rightOutlinedBox {
  padding-left: 22px;
  padding-right: 8px;
  img {
    width: 30px;
    height: 30px;
  }
}

.taskDetailsBody {
  flex: 1;
}
</style>
