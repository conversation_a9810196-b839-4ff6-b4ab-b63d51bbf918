<template>
  <div class="task-page">
    <ContHeader type="back" />
    <ul class="menu">
      <li
        v-for="(item, index) in menuList"
        :key="item.value"
        @click="changeMenu(item)"
      >
        <div class="top-wrap">
          <div class="icon-wrap" :style="activeStyle(item)">
            <i
              :class="['icon', 'iconfont', item.icon]"
              :style="disableStyle(item)"
            ></i>
          </div>
          <img
            v-if="index !== menuList.length - 1"
            class="right-wrap"
            src="@/assets/images/line-bar.png"
            alt="line-bar.png"
            :style="disableImg"
          />
        </div>
        <p class="ellipsis title">{{ item.title }}</p>
      </li>
    </ul>
    <Info
      v-if="state.activeKey === 'info'"
      :monitoringId="state.id"
      :monitoringJobStatus="state.monitoringJobStatus"
    />
    <DataSampling
      v-if="state.activeKey === 'dataSampling'"
      :monitoringId="state.id"
      :monitoringJobStatus="state.monitoringJobStatus"
    />
    <!-- 发起测试组件 -->
    <LaunchTest
      :monitoringId="state.id"
      v-if="state.activeKey === 'launchTest'"
    />
    <SponsorQuestionnaire
      :monitoringId="state.id"
      v-if="state.activeKey === 'question'"
    />

    <Marking :monitoringId="state.id"
    v-if="state.activeKey === 'mark'"/>

  </div>
</template>

<script setup>
import { theme } from 'ant-design-vue';
//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
const { token } = theme.useToken();
import Info from './Info.vue';
import DataSampling from './dataSampling/index.vue';
import LaunchTest from './launchTest/index.vue';
import SponsorQuestionnaire from './sponsorQuestionnaire/index.vue';
import Marking from "./mark/index.vue"



import { computed, onMounted } from 'vue';
const route = useRoute();
const router = useRouter()

const menuList = [
  {
    icon: 'icon-xinxicaiji',
    title: '信息采集',
    value: 'info',
  },
  {
    icon: 'icon-shujuchouyang',
    title: '数据抽样',
    value: 'dataSampling',
  },
  {
    icon: 'icon-faqiceshi',
    title: '发起测试',
    value: 'launchTest',
  },
  {
    icon: 'icon-faqiwenjuan',
    title: '发起问卷',
    value: 'question',
  },
  {
    icon: 'icon-yuejuan',
    title: '发起阅卷',
    value: 'mark',
  },
  {
    icon: 'icon-zhuanjiapinggu',
    title: '专家评估',
    value: 'evaluate',
  },
  {
    icon: 'icon-baogaochakan',
    title: '报告查看',
    value: 'report',
  },
];

// *********************
// Hooks Function
// *********************
// monitoringJobStatus 状态
//     COLLECTING()"TO_COLLECT": "待采集")
//     COLLECTING("COLLECTING", "采集中")
//     COLLECTING_FINISHED("COLLECTING_FINISHED", "已采集")
//     SAMPLING("SAMPLING", "抽样中")
//     SAMPLING_FINISHED("SAMPLING_FINISHED", "已抽样")
//     EXAMINATION("EXAMINATION", "测试中")
//     EXAMINATION_FINISHED("EXAMINATION_FINISHED", "已测试")
//     QUESTIONNAIRE("QUESTIONNAIRE", "问卷进行中")
//     QUESTIONNAIRE_FINISHED("QUESTIONNAIRE_FINISHED", "已完成问卷")
//     SCORING("SCORING", "阅卷中")
//     SCORING_FINISHED("SCORING_FINISHED", "已阅卷")
//     ASSESS("ASSESS", "专家评估中")
//     ASSESS_FINISHED("ASSESS_FINISHED", "已评估")
//     REPORT("REPORT", "生成报告中")
//     FINISHED("FINISHED", "已完成")

const state = reactive({
  activeKey: route.query?.menuKey || 'info',
  id: route.query.id,
  monitoringJobStatus: '',
});

const activeStyle = computed(() => {
  return item => {
    if (item.value === state.activeKey) {
      return {
        color: '#ffffff',
        borderColor: token.value.colorPrimaryBg,
        background: token.value.colorPrimaryBorderHover,
      };
    } else {
      return {
        background: token.value.colorPrimaryBg,
        borderColor: '#ffffff',
        color: token.value.colorPrimaryBorderHover,
      };
    }
  };
});

const disableStyle = computed(() => {
  return item => {
    return route.query.fake && state.activeKey !== item.value
      ? {
          color: ' #999',
          background: '#eee',
          borderRadius: '50%',
        }
      : {};
  };
});

const disableImg = computed(() => {
  return route.query.fake ? 'filter: grayscale(100%)' : '';
});

const changeMenu = item => {
  // if (!route.query?.menuKey) 
  // state.activeKey = item.value;  
  router.replace({
    path:route.path,
    query:{
      ...route.query,
      menuKey:item.value
    }
  })
 
};

function getMonitoringDetail(id) {
  if (!id) {
    return false;
  }
  http.post('/manage/monitoring/getDetails', { id }).then(res => {
    if (res.code == 0) {
      console.log('获取监测详情', res.data);
      state.monitoringJobStatus = res.data?.monitoringJobStatus;
    } else {
    }
  });
}

onMounted(() => {
  const { id } = route.query;
  
  getMonitoringDetail(id);
});
</script>

<style lang="less" scoped>
.task-page {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.menu {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 15px 231px 16px;
  .top-wrap {
    display: flex;
    align-items: center;
  }
  .icon-wrap {
    box-sizing: border-box;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    cursor: pointer;
    border-radius: 50%;
    border: 4px solid #b8eccd;
    background: #4dd082;
    .icon {
      position: absolute;
      font-size: 40px;
      z-index: 2;
    }
  }

  .title {
    // width: 48px;
    // text-align: center;
    font-weight: 400;
    font-size: 12px;
    color: #595959;
    padding-top: 7px;
    cursor: pointer;
  }
}
</style>
