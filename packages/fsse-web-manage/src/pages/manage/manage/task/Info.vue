<template>
  <div class="info-page">
    <div class="empty-wrap" v-if="state.jobStatus === 'pending'">
      <img src="@/assets/images/pic-xxcj.png" alt="pic-xxcj.png" />
      <a-button type="primary" class="empty-btn" @click="handleStart(false)">
        开始采集
      </a-button>
    </div>

    <div class="info-wrap" v-else>
      <div class="info-left-wrap">
        <ul>
          <li
            v-for="(item, index) in leftlist"
            :key="item.id"
            @click="changeMenu(item)"
          >
            <p class="index">{{ index + 1 }}</p>
            <p class="title" :style="typeStyle(item)">{{ item.name }}</p>
          </li>
        </ul>
        <a-button type="primary" class="setting-btn" @Click="handleStart(true)">
          <div flex flex-items-center flex-justify-center h-full>
            <i class="iconfont icon-xitongshezhi" mr-2 mt-2 />
            <span>设置</span>
          </div>
        </a-button>
      </div>
      <div class="info-right-wrap">
        <searchForm
          v-model:formState="queryType[state.type].query"
          :formList="formList"
          @submit="searchQuery"
          @reset="
            queryType[state.type].reset({ collectJobId: state.record.id })
          "
        />

        <div class="feat-group-btn">
          <div class="left-feat">
            <span class="title">当前已采集：</span>
            <a-progress
              w-240
              h-14
              :percent="
                (state.process.completedCount / state.process.totalCount) * 100
              "
              :showInfo="false"
              :strokeColor="token.colorPrimaryBorder"
              :trailColor="token.colorPrimaryBg"
              :size="14"
            />
            <span class="sum"
              >{{ state.process.completedCount }}/{{
                state.process.totalCount
              }}</span
            >
          </div>
          <div class="right-feat">
            <a-button @click="handleCollection"> 采集指标 </a-button>
            <a-button ml-12 @click="handleExport"> 导出 </a-button>
            <a-button ml-12 @click="handleImport" v-if="state.type == 'city'">
              导入
            </a-button>
          </div>
        </div>

        <div class="ETable-wrap">
          <ETable
            hash="Info"
            :loading="queryType[state.type].page.loading"
            :columns="state.columns"
            :dataSource="sourceList"
            :total="queryType[state.type].page.total"
            @paginationChange="queryType[state.type].paginationChange"
            :current="queryType[state.type].query.pageNo"
          >
            <template #headerCell-layout>
              <span style="cursor: pointer" @click="handeSeatLayout">
                座位布局图 <question-circle-outlined class="iconColor" />
              </span>
            </template>
            <template #layout="{ record }">
              <a-tooltip>
                <template #title>
                  <img
                    :src="layoutImages[record.layout - 1]"
                    alt="配置信息"
                    style="width: 207px; height: 279px"
                  />
                </template>
                <a-button type="text">样式{{ record.layout }}</a-button>
              </a-tooltip>
            </template>
            <template #configuration="{ record }">
              <a-button
                type="link"
                class="btn-link-color"
                @click="handerComputerConf(record)"
                >查看</a-button
              >
            </template>
            <template #provideHeadphone="{ record }">
              {{ record.provideHeadphone ? '是' : '否' }}
            </template>
            <template #row="{ record }">
              {{ `${record.row}*${record.cell}` }}
            </template>
            <template #operate="{ record }">
              <a-button
                v-if="state.type === 'city'"
                type="link"
                class="btn-link-color"
                @click="opeModify(record)"
                >修改</a-button
              >
              <a-button
                type="link"
                class="btn-link-color"
                @click="viewProgress(record)"
                >查看进度</a-button
              >
            </template>
          </ETable>
        </div>
      </div>
    </div>

    <StartCollecting ref="startCollecting" @confirm="StartCollectConfirm" />
    <Collection ref="collectionRef" @confirm="collectionConfirm" />
    <Modify
      ref="modifyRef"
      :provinceList="state.provinceList"
      @confirm="modifyConfirm"
    />
    <!-- 座位布局图 -->
    <SeatLayoutModal ref="seatLayoutModalRef" />
    <!--  电脑配置信息 -->
    <ComputerConfModal ref="computerConfModalRef" />
    <ImportModel ref="ImportRef" />
  </div>
</template>

<script setup>
import SeatLayoutModal from './seatLayoutModal.vue';
import ComputerConfModal from './computerConfModal.vue';
import StartCollecting from './StartCollecting.vue';
import Collection from './Collection.vue';
import Modify from './Modify.vue';
import seat1 from '@/assets/images/seat1.png';
import seat2 from '@/assets/images/seat2.png';
import seat3 from '@/assets/images/seat3.png';
import { columnsMapType } from './config';
import { theme } from 'ant-design-vue';

import ImportModel from '@/components/common/Import/index.vue'

//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
const { token } = theme.useToken();

const mainStore = useStore();
let level = mainStore?.userInfo?.fsseOrg?.level;

const layoutImages = shallowRef([seat1, seat2, seat3]);
const startCollecting = ref(null);
const collectionRef = ref(null);
const modifyRef = ref(null);

const props = defineProps({
  monitoringJobStatus: {
    type: String,
    default: '',
  },
  monitoringId: {
    type: String,
    default: '',
  },
});

const levelItemsMap = {
  1: [
    { name: '市信息采集', id: 'city' },
    { name: '区/县信息采集', id: 'district' },
    { name: '学校信息采集', id: 'school' },
    { name: '学生信息采集', id: 'student' },
    // { name: '家长信息采集', id: 'eltern' },
    { name: '教师信息采集', id: 'teacher' },
    { name: '监测室信息采集', id: 'monitoring_room' },
    { name: '监测员信息采集', id: 'monitoring_member' },
  ],
  2: [
    { name: '区/县信息采集', id: 'district' },
    { name: '学校信息采集', id: 'school' },
    { name: '学生信息采集', id: 'student' },
    // { name: '家长信息采集', id: 'eltern' },
    { name: '教师信息采集', id: 'teacher' },
    { name: '监测室信息采集', id: 'monitoring_room' },
    { name: '监测员信息采集', id: 'monitoring_member' },
  ],
  3: [
    { name: '学校信息采集', id: 'school' },
    { name: '学生信息采集', id: 'student' },
    // { name: '家长信息采集', id: 'eltern' },
    { name: '教师信息采集', id: 'teacher' },
    { name: '监测室信息采集', id: 'monitoring_room' },
    { name: '监测员信息采集', id: 'monitoring_member' },
  ],
  4: [
    { name: '学生信息采集', id: 'student' },
    // { name: '家长信息采集', id: 'eltern' },
    { name: '教师信息采集', id: 'teacher' },
    { name: '监测室信息采集', id: 'monitoring_room' },
    { name: '监测员信息采集', id: 'monitoring_member' },
  ],
};

const defaultItems = [
  { name: '市信息采集', id: 'city' },
  { name: '区/县信息采集', id: 'district' },
  { name: '学校信息采集', id: 'school' },
  { name: '学生信息采集', id: 'student' },
  { name: '家长信息采集', id: 'eltern' },
  { name: '教师信息采集', id: 'teacher' },
  { name: '监测室信息采集', id: 'monitoring_room' },
  { name: '监测员信息采集', id: 'monitoring_member' },
];

const leftlist = computed(() => {
  return levelItemsMap[level] || defaultItems; // 如果 level 不在 map 中，返回默认项
});

const state = reactive({
  type: leftlist.value[0].id,
  columns: [],
  record: {},
  provinceList: [],
  cityList: [],
  districtList: [],
  jobStatus: 'pending',
  process: {
    completedCount: 0,
    totalCount: 0,
  },
  collectList: [],
});

const commonFormList = ref([
  {
    type: 'select',
    value: 'province',
    label: '省',
    list: state.provinceList,
    attrs: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
      onChange: val => {
        queryType[state.type].query.city = null;
        queryType[state.type].query.district = null;
        state.cityList.splice(0, state.cityList.length);
        getAreaList({ pid: val }, 'cityList');
      },
    },
  },
  {
    type: 'select',
    value: 'city',
    label: '市',
    list: state.cityList,
    attrs: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
      onChange: val => {
        queryType[state.type].query.district = null;
        state.districtList.splice(0, state.districtList.length);
        getAreaList({ pid: val }, 'districtList');
      },
    },
  },
]);

const typeStyle = computed(() => {
  return item => {
    if (item.id === state.type) {
      return {
        background: token.value.colorPrimaryBg,
        color: token.value.colorPrimary,
      };
    } else {
      return {
        background: '#fff',
        color: '#262626',
      };
    }
  };
});

const formList = computed(() => {
  if (state.type === 'city') {
    // 市
    return commonFormList.value;
  } else if (state.type === 'district') {
    // 区/县
    return [
      ...commonFormList.value,
      {
        type: 'select',
        value: 'district',
        label: '区/县',
        list: state.districtList,
        attrs: {
          fieldNames: {
            label: 'name',
            value: 'id',
          },
        },
      },
    ];
  } else if (state.type === 'school') {
    // 学校
    return [
      ...commonFormList.value,
      {
        type: 'select',
        value: 'district',
        label: '区/县',
        list: state.districtList,
        attrs: {
          fieldNames: {
            label: 'name',
            value: 'id',
          },
        },
      },
      {
        type: 'input',
        value: 'name',
        label: '学校名称',
      },
    ];
  } else {
    // 其他
    return [
      ...commonFormList.value,
      {
        type: 'select',
        value: 'district',
        label: '区/县',
        list: state.districtList,
        attrs: {
          fieldNames: {
            label: 'name',
            value: 'id',
          },
        },
      },
      {
        type: 'input',
        value: 'schoolName',
        label: '学校名称',
      },
    ];
  }
});

const sourceList = computed(() => {
  const dataSource = [];
  queryType[state.type].page.list.forEach(item => {
    if (item.indicators) {
      item.indicators.forEach(i => {
        item[i.indicatorId] = i.indicatorRuleValue;
      });
    }
    dataSource.push(item);
  });
  return dataSource;
});

// 查询列表
const queryType = {
  city: useList('/manage/collect/job/city/page'),
  district: useList('/manage/collect/job/district/page'),
  school: useList('/manage/collect/job/school/page'),
  student: useList('/manage/collect/job/student/page'),
  eltern: useList('/manage/collect/job/eltern/page'),
  teacher: useList('/manage/collect/job/teacher/page'),
  monitoring_room: useList('/manage/collect/job/room/page'),
  monitoring_member: useList('/manage/collect/job/room/person/page'),
};

// 动态查询
const searchQuery = () => {
  const areaId =
    queryType[state.type].query.district ||
    queryType[state.type].query.city ||
    queryType[state.type].query.province ||
    null;
  queryType[state.type].getList({
    collectJobId: state.record.id,
    areaId,
  });
};

// 更新查询条件
function updateQuery() {
  if (state.type === 'city') {
    // 市
    return {
      province: null,
      city: null,
    };
  } else if (state.type === 'district') {
    // 区/县
    return {
      province: null,
      city: null,
      district: null,
    };
  } else if (state.type === 'school') {
    return {
      province: null,
      city: null,
      district: null,
      name: '',
    };
  } else {
    return {
      province: null,
      city: null,
      district: null,
      schoolName: '',
    };
  }
}

// 作为布局图
const seatLayoutModalRef = ref(null);
const handeSeatLayout = () => {
  seatLayoutModalRef.value.show();
};

const computerConfModalRef = ref(null);
// 查看电脑配置信息
const handerComputerConf = item => {
  computerConfModalRef.value.computerConfData = toRaw(item);
  computerConfModalRef.value.showModal();
};

// 切换列菜单
function changeMenu(item, params = {}, isView = false) {
  state.type = item.id;
  // 获取指标列表
  getCollectList();
  // 获取采集进度
  getProgress();
  if (!isView) {
    state.cityList.splice(0, state.cityList.length);
    state.districtList.splice(0, state.districtList.length);
  }
  // 合并查询数据
  const query = {
    pageNo: 1,
    pageSize: 10,
    ...updateQuery(),
    ...params,
  };
  Object.assign(queryType[state.type].query, query);
  searchQuery();
}

const viewProgress = item => {
  const index = leftlist.value.findIndex(i => i.id === state.type);
  // 根据列表数据获取下拉数据
  getAreaList({ pid: item.provinceId }, 'cityList');
  getAreaList({ pid: item.cityId }, 'districtList');

  const params = {
    province: item.provinceId,
    city: item.cityId,
    district: item.districtId,
  };
  // 携带上条件
  changeMenu(leftlist.value[index + 1], params, true);
};

// 修改
const opeModify = item => {
  const indicators = state.collectList
    .filter(i => i.checked)
    .map(i => ({
      indicatorId: i.id,
      indicatorRuleType: i.indicatorRuleType,
      indicatorRuleValue: null,
      indicatorType: i.indicatorType,
      name: i.name,
    }));
  if (!item.indicators) {
    item.indicators = indicators;
  }
  const data = JSON.parse(JSON.stringify(item));
  modifyRef.value.showModal(data);
};

// 设置
const handleStart = (isEdit = false) => {
  startCollecting.value.showModal(props.monitoringId, isEdit);
};

// 采集指标
const handleCollection = () => {
  collectionRef.value.showModal(state.collectList);
};

const ImportRef = ref(null);
// 导入
const handleImport = () => {
  ImportRef.value.show({
    staticFile: false, // 需要动态下载文件
    importUrl: '/manage/collect/job/import', // 导入链接
    importParams: {
      collectJobId: state.record.id,
      stepId: 1,
    }, // 导入参数
    exprots: [
      {
        fileName: '市采集信息导入模板', // 文件名
        downloadText: '市采集信息导入模板', // 按钮显示名称
        exprotUrl: '/manage/collect/job/city/export', // 导出
        params: {
          collectJobId: state.record.id,
          template: true,
        },
      },
    ],
  });
};

const exportUrl = {
  city: '/manage/collect/job/city/export',
  district: '/manage/collect/job/district/export',
  school: '/manage/collect/job/school/export',
  student: '/manage/collect/job/student/export',
  eltern: '/manage/collect/job/eltern/export',
  teacher: '/manage/collect/job/teacher/export',
  monitoring_room: '/manage/collect/job/room/export',
  monitoring_member: '/manage/collect/job/room/person/export',
};
// 导出
const handleExport = async () => {
  const item = leftlist.value.find(i => i.id === state.type);
  const url = exportUrl[item.id];
  const areaId =
    queryType[state.type].query.district ||
    queryType[state.type].query.city ||
    queryType[state.type].query.province ||
    null;
  const name = queryType[state.type].query.school || null;
  const params = {
    schoolName: queryType[state.type].query.schoolName,
    collectJobId: state.record.id,
    areaId,
    name,
  };
  http.download(url, params, item.name);
};

const getDetail = monitoringId => {
  return new Promise(resolve => {
    http.post('/manage/collect/job/getDetails', { monitoringId }).then(res => {
      if (!res.data) return;
      state.jobStatus = res.data.jobStatus;
      state.record = res.data;
      resolve(true);
    });
  });
};

// 获取进度
function getProgress() {
  http
    .post('/manage/collect/job/getJobProgress', {
      collectJobId: state.record.id,
      jobType: state.type,
    })
    .then(res => {
      state.process = res.data;
    });
}

// 采集确认
const StartCollectConfirm = async () => {
  await getDetail(props.monitoringId);
  getProgress();
  getCollectList();
  searchQuery();
};

// 任务指标
const collectionConfirm = data => {
  const params = {
    collectJobId: state.record.id,
    indicatorType: state.type,
    indicatorIds: data.indicatorIds,
  };
  http
    .post('/manage/collect/job/saveCollectJobIndicator', params)
    .then(async () => {
      YMessage.success('指标保存成功');
      getCollectList();
      searchQuery();
    });
};

// 修改确认
const modifyConfirm = () => {
  searchQuery();
};

// 获取省市区
function getAreaList(params, type) {
  http.post('/manage/area/list', params).then(res => {
    state[type].push(...res.data);
  });
}

// 获取任务指标列表
function getCollectList() {
  const item = { title: '操作', dataIndex: 'operate', width: 140 };
  http
    .post('/manage/collect/job/listCollectJobIndicator', {
      collectJobId: state.record.id,
      indicatorType: state.type,
    })
    .then(res => {
      if (!res.data) res.data = [];
      state.collectList = res.data;
      const columns = res.data
        .filter(i => i.checked)
        .map(i => ({
          title: i.name,
          dataIndex: i.id,
        }));
      state.columns = [...columnsMapType[state.type], ...columns];
      // 只有市/区有操作列
      if (['city', 'district'].includes(state.type)) {
        state.columns.push(item);
      }
    });
}

onMounted(async () => {
  getAreaList({ type: 1 }, 'provinceList');
  await getDetail(props.monitoringId);
  getProgress();
  getCollectList();
  searchQuery();
});
</script>

<style lang="less" scoped>
.empty-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 10%;
}

.info-page {
  height: 100%;
  overflow: hidden;
}

.info-wrap {
  display: flex;
  border-top: 1px solid #ecedf1;
  height: 100%;
}

.info-left-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 165px;
  border-right: 1px solid #ecedf1;
  height: 100%;
  ul {
    display: flex;
    flex-direction: column;
    padding: 16px 8px 0 10px;
    flex: 1;
    overflow-y: auto;

    li {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      cursor: pointer;
    }

    .index {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      background: #ffffff;
      border: 1px solid var(--primary-color);
      border-radius: 20px;
      font-weight: 500;
      font-size: 14px;
      color: var(--primary-color);
    }

    .title {
      box-sizing: border-box;
      flex: 1;
      margin-left: 10px;
      border-radius: 4px;
      line-height: 32px;
      padding: 0 8px;
      font-weight: 400;
      font-size: 14px;
      color: #262626;
      &:hover {
        color: var(--primary-color) !important;
      }
    }
  }

  .setting-btn {
    width: 120px;
    height: 32px;
    border-radius: 4px;
    margin: 10px auto;
  }
}

.info-right-wrap {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 16px;
  flex: 1;
  overflow-y: auto;

  .feat-group-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 16px 0;
    .left-feat {
      display: flex;
      align-items: center;
      .title {
        font-weight: 400;
        font-size: 14px;
        color: #262626;
        text-wrap: nowrap;
      }
      .sum {
        font-weight: 600;
        font-size: 16px;
        color: var(--primary-color);
      }
    }
  }
}

.iconColor {
  color: var(--primary-color);
}
</style>
