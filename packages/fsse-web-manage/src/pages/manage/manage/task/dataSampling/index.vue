<template>
  <div class="data_sampling_container">
    <div class="empty-wrap" v-if="state.record.jobStatus !== 'finished'">
      <template v-if="state.record.jobStatus === 'in_progress'">
        <img w-300 src="@/assets/images/pic-cyz.png" alt="pic-cyz.png" />
        <div font-size-21>抽样中，请等待...</div>
        <a-button
          type="primary"
          mt-20
          w-88
          h-32
          font-size-14
          @click="handleStart"
        >
          点击查看
        </a-button>
      </template>
      <template v-else>
        <img src="@/assets/images/pic-kscy.png" alt="pic-kscy.png" />
        <a-button type="primary" class="empty-btn" @click="handleStart">
          开始抽样
        </a-button>
      </template>
    </div>

    <div class="info-wrap" v-else>
      <div class="info-left-wrap">
        <ul>
          <li
            v-for="(item, index) in list"
            :key="item.id"
            @click="changeMenu(item)"
          >
            <p class="index">{{ index + 1 }}</p>
            <p class="title" :style="typeStyle(item)">{{ item.name }}</p>
          </li>
        </ul>
        <a-button
          type="primary"
          class="setting-btn"
          @Click="handleNotice"
          :disabled="state.record.jobStatus !== 'finished'"
        >
          <div flex flex-items-center flex-justify-center h-full>
            <span>下发通知</span>
          </div>
        </a-button>
      </div>
      <div class="info-right-wrap">
        <searchForm
          v-model:formState="queryType[state.type].query"
          :formList="commonFormList"
          @submit="searchQuery"
          @reset="
            queryType[state.type].reset({
              collectJobId: state.record.collectJobId,
            })
          "
        />

        <div class="feat-group-btn">
          <div class="left-feat">
            <span class="title"
              >抽样人：{{ state.info[changType].samplingMemberName }}</span
            >
            <span class="title" ml-32
              >抽样时间：{{ state.info[changType].createTime }}</span
            >
          </div>
          <div class="right-feat">
            <a-button ml-12 @click="handleExport"> 导出 </a-button>
          </div>
        </div>

        <div class="ETable-wrap">
          <ETable
            hash="DataAsmpling"
            :loading="queryType[state.type].page.loading"
            :columns="state.columns"
            :dataSource="sourceList"
            :total="queryType[state.type].page.total"
            @paginationChange="queryType[state.type].paginationChange"
            :current="queryType[state.type].query.pageNo"
          >
            <template #emptyText-slot>
              <!-- <div class="empty_box" v-if="state.type !== 'school'">
                <img src="@/assets/images/jc-empty.png" alt="jc-empty.png" />
                <div>
                  {{ typeName }}仅在考前2小时可查看.请前往
                  <a-button type="link" class="btn-link-color"
                    >【发起测试】</a-button
                  >
                  设置一次测试吧!
                </div>
              </div> -->
              <div>
                <img w-180 src="@/assets/images/empty.png" alt="empty.png" />
                <div>暂无数据</div>
              </div>
            </template>
          </ETable>
        </div>
      </div>
    </div>
    <SampDrawer ref="SampDrawerRef" @cancel="cancel" />
  </div>
</template>

<script setup>
import { computed } from 'vue';
import SampDrawer from './components/sampDrawer.vue';
import { columnsMapType } from './config';
import { theme } from 'ant-design-vue';
//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
const { token } = theme.useToken();
const props = defineProps({
  monitoringId: {
    type: [String, Number],
    default: '',
  },
});

const state = reactive({
  type: 'school',
  columns: [],
  monitoringJobStatus: '',
  record: {},
  provinceList: [],
  cityList: [],
  districtList: [],
  info: {
    school: {},
    student: {},
  },
});

const list = [
  {
    name: '样本学校',
    id: 'school',
  },
  {
    name: '样本学生',
    id: 'student',
  },
  {
    name: '样本教师',
    id: 'teacher',
  },
  // {
  //   name: '样本家长',
  //   id: 'eltern',
  // },
];

const changType = computed(() => {
  const typeObj = {
    school: 'school',
    teacher: 'school',
    student: 'student',
    eltern: 'student',
  };
  return typeObj[state.type];
});

const commonFormList = ref([
  {
    type: 'select',
    value: 'province',
    label: '省',
    list: state.provinceList,
    attrs: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
      onChange: val => {
        queryType[state.type].query.city = null;
        queryType[state.type].query.district = null;
        state.cityList.splice(0, state.cityList.length);
        getAreaList({ pid: val }, 'cityList');
      },
    },
  },
  {
    type: 'select',
    value: 'city',
    label: '市',
    list: state.cityList,
    attrs: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
      onChange: val => {
        queryType[state.type].query.district = null;
        state.districtList.splice(0, state.districtList.length);
        getAreaList({ pid: val }, 'districtList');
      },
    },
  },
  {
    type: 'select',
    value: 'district',
    label: '区/县',
    list: state.districtList,
    attrs: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
  },
  {
    type: 'input',
    value: 'schoolName',
    label: '学校名称',
  },
]);

// *********************
// Hooks Function
// *********************

const typeStyle = computed(() => {
  return item => {
    if (item.id === state.type) {
      return {
        background: token.value.colorPrimaryBg,
        color: token.value.colorPrimary,
      };
    } else {
      return {
        background: '#fff',
        color: '#262626',
      };
    }
  };
});

const typeName = computed(() => {
  return list.find(i => i.id === state.type).name;
});

// *********************
// Default Function
// *********************

// 查询列表
const queryType = {
  school: useList('/manage/collect/job/school/page'),
  student: useList('/manage/collect/job/student/page'),
  teacher: useList('/manage/collect/job/teacher/page'),
  eltern: useList('/manage/collect/job/eltern/page'),
};

// 动态查询
const searchQuery = () => {
  const areaId =
    queryType[state.type].query.district ||
    queryType[state.type].query.city ||
    queryType[state.type].query.province ||
    null;
  const name = queryType[state.type].query.school || '';
  queryType[state.type].getList({
    samplingJobId: state.record.id, //抽样任务ID
    collectJobId: state.record.collectJobId,
    areaId,
    name,
  });
};

function changeMenu(item) {
  state.type = item.id;
  // 获取指标列表
  getCollectList();
  // 合并查询数据
  const query = {
    pageNo: 1,
    pageSize: 10,
    province: null,
    city: null,
    district: null,
    schoolName: '',
  };
  Object.assign(queryType[state.type].query, query);
  searchQuery();
}

// 下发抽样结果
const handleNotice = () => {
  http
    .post('/manage/sampling/job/samplingResultSend', { id: state.record.id })
    .then(() => {
      YMessage.success('下发成功');
    });
};

const exportUrl = {
  city: '/manage/collect/job/city/export',
  district: '/manage/collect/job/district/export',
  school: '/manage/collect/job/school/export',
  student: '/manage/collect/job/student/export',
  eltern: '/manage/collect/job/eltern/export',
  teacher: '/manage/collect/job/teacher/export',
  monitoring_room: '/manage/collect/job/room/export',
  monitoring_member: '/manage/collect/job/room/person/export',
};

// 导出
const handleExport = async () => {
  const item = list.find(i => i.id === state.type);
  const url = exportUrl[item.id];
  const areaId =
    queryType[state.type].query.district ||
    queryType[state.type].query.city ||
    queryType[state.type].query.province ||
    null;
  const name = queryType[state.type].query.school || null;
  const params = {
    samplingJobId: state.record.id, //抽样任务ID
    schoolName: queryType[state.type].query.schoolName,
    collectJobId: state.record.collectJobId,
    areaId,
    name,
  };
  http.download(url, params, item.name);
};

const SampDrawerRef = ref(null);
// 开始抽样
const handleStart = () => {
  if (!state.record.samplingJobDetails)
    return YMessage.warning('请先进行信息采集');

  console.log('state.record:', state.record);
  const data = JSON.parse(JSON.stringify(state.record.samplingJobDetails));
  SampDrawerRef.value.show(data, state.record.jobStatus);
};

// 获取省市区
function getAreaList(params, type) {
  http.post('/manage/area/list', params).then(res => {
    state[type].push(...res.data);
  });
}

// 获取任务指标列表
function getCollectList() {
  http
    .post('/manage/collect/job/listCollectJobIndicator', {
      collectJobId: state.record.collectJobId,
      indicatorType: state.type,
    })
    .then(res => {
      if (!res.data) res.data = [];
      state.collectList = res.data;
      const columns = res.data.map(i => ({
        title: i.name,
        dataIndex: i.id,
      }));
      state.columns = [...columnsMapType[state.type], ...columns];
    });
}

// 获取抽样详情
const getDetail = () => {
  return new Promise(resolve => {
    http
      .post('/manage/sampling/job/getDetails', {
        monitoringId: props.monitoringId,
      })
      .then(res => {
        if (!res.data) return;
        state.record = res.data;
        if (res.data.samplingJobDetails) {
          res.data.samplingJobDetails.forEach(
            item => (state.info[item.samplingDataType] = item)
          );
        }
        resolve();
      });
  });
};

const sourceList = computed(() => {
  const dataSource = [];
  queryType[state.type].page.list.forEach(item => {
    if (item.indicators) {
      item.indicators.forEach(i => {
        item[i.indicatorId] = i.indicatorRuleValue;
      });
    }
    dataSource.push(item);
  });
  return dataSource;
});

// 抽屉关闭
const cancel = () => {
  getDetail();
};

onMounted(async () => {
  getAreaList({ type: 1 }, 'provinceList');
  // 抽样详情
  await getDetail();
  getCollectList();
  searchQuery();
});
</script>

<style lang="less" scoped>
.empty-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 17%;
  .empty-btn {
    width: 158px;
    height: 50px;
    font-size: 18px;
  }
}

.data_sampling_container {
  height: 100%;
  overflow: hidden;
}

.info-wrap {
  display: flex;
  border-top: 1px solid #ecedf1;
  height: 100%;
}

.info-left-wrap {
  display: flex;
  flex-direction: column;
  width: 165px;
  border-right: 1px solid #ecedf1;
  height: 100%;
  ul {
    display: flex;
    flex-direction: column;
    padding: 16px 8px 0 10px;
    flex: 1;
    overflow-y: auto;

    li {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      cursor: pointer;
    }

    .index {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      background: #ffffff;
      border: 1px solid var(--primary-color);
      border-radius: 20px;
      font-weight: 500;
      font-size: 14px;
      color: var(--primary-color);
    }

    .title {
      box-sizing: border-box;
      flex: 1;
      margin-left: 10px;
      border-radius: 4px;
      line-height: 32px;
      padding: 0 8px;
      font-weight: 400;
      font-size: 14px;
      color: #262626;
      &:hover {
        color: var(--primary-color) !important;
      }
    }
  }

  .setting-btn {
    width: 120px;
    height: 32px;
    border-radius: 4px;
    margin: 10px auto;
  }
}

.info-right-wrap {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 16px;
  flex: 1;
  overflow-y: auto;

  .feat-group-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 16px 0;
    .left-feat {
      display: flex;
      align-items: center;
      height: 32px;
      .title {
        font-weight: 400;
        font-size: 14px;
        color: #262626;
        text-wrap: nowrap;
      }
      .sum {
        font-weight: 600;
        font-size: 16px;
        color: var(--primary-color);
      }
    }
  }
}
</style>
