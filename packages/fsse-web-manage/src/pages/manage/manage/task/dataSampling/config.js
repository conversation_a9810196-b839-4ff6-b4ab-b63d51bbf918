// 学校
const schoolColumns = [
  { title: '地市名称', dataIndex: 'cityName', key: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName', key: 'districtName' },
  { title: '学校名称', dataIndex: 'name', key: 'name' },
  { title: '学校简称', dataIndex: 'shortName', key: 'shortName' },
  {
    title: '状态',
    dataIndex: 'collectStatus',
    key: 'collectStatus',
    customRender: ({ text }) => (text == '0' ? '未上报' : '已上报'),
  },
  { title: '上报人', dataIndex: 'reportUserName', key: 'reportUserName' },
  { title: '上报时间', dataIndex: 'reportTime', key: 'reportTime'  },
];

// 学生
// const studentColumns = [
//   { title: '地市名称', dataIndex: 'cityName' },
//   { title: '区/县名称', dataIndex: 'districtName' },
//   { title: '学校名称', dataIndex: 'schoolName' },
//   { title: '学生姓名', dataIndex: 'name', key: 'name' },
//   { title: '所在年级', dataIndex: 'grade', key: 'grade' },
//   { title: '班级', dataIndex: 'classesName', key: 'classesName' },
//   { title: '年龄（岁）', dataIndex: 'age', key: 'age' },
//   { title: '学籍号', dataIndex: 'studentCode', key: 'studentCode' },
// ];

const studentColumns = [
  { title: '地市名称', dataIndex: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName' },
  { title: '学校名称', dataIndex: 'schoolName' },
  { title: '年级', dataIndex: 'grade', key: 'grade' },
  { title: '现所在班级', dataIndex: 'classesName', key: 'classesName' },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '性别', dataIndex: 'gender', key: 'gender' },
  { title: '学籍号', dataIndex: 'studentCode', key: 'studentCode' },
  { title: '出生日期', dataIndex: 'birthday', key: 'birthday' },
  { title: '备注', dataIndex: 'remark', key: 'remark' },
];


const relationsObj = {
  1: '父亲',
  2: '母亲',
  3: '爷爷',
  4: '奶奶',
  5: '外公',
  6: '外婆',
  7: '其他',
  default: '',
};

function getrelations(key) {
  return relationsObj[key] || relationsObj.default;
}

// 家长
const parentColumns = [
  { title: '地市名称', dataIndex: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName' },
  { title: '学校名称', dataIndex: 'schoolName' },
  { title: '学生姓名', dataIndex: 'studentName', key: 'studentName' },
  { title: '所在年级', dataIndex: 'grade', key: 'grade' },
  { title: '班级', dataIndex: 'classesName', key: 'classesName' },
  { title: '家长姓名', dataIndex: 'name', key: 'name' },
  { title: '家长电话', dataIndex: 'phone', key: 'phone' },
  {
    title: '与学生关系',
    dataIndex: 'relations',
    key: 'relations',
    customRender: ({ text }) => getrelations(text),
  },
];

// 教师
// const teacherColumns = [
//   { title: '地市名称', dataIndex: 'cityName' },
//   { title: '区/县名称', dataIndex: 'districtName' },
//   { title: '学校名称', dataIndex: 'schoolName' },
//   { title: '教师姓名', dataIndex: 'name', key: 'name' },
//   { title: '手机号', dataIndex: 'phone', key: 'phone' },
//   { title: '出生年月', dataIndex: 'birthday', key: 'birthday' },
//   { title: '上学期任教科目', dataIndex: 'subjectName', key: 'subjectName' },
//   { title: '所属学段', dataIndex: 'section', key: 'section' },
//   {
//     title: '上学期是否为班主任',
//     dataIndex: 'isLeader',
//     key: 'isLeader',
//     customRender: ({ text }) => (text === false ? '否' : '是'),
//   },
//   {
//     title: '是否为管理干部',
//     dataIndex: 'isManager',
//     key: 'isManager',
//     customRender: ({ text }) => (text === false ? '否' : '是'),
//   },
//   {
//     title: '是否为校长',
//     dataIndex: 'isPrincipal',
//     key: 'isPrincipal',
//     customRender: ({ text }) => (text === false ? '否' : '是'),
//   },
// ];


const teacherColumns = [
  { title: '地市名称', dataIndex: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName' },
  { title: '学校名称', dataIndex: 'schoolName' },
  { title: '教师姓名', dataIndex: 'name', key: 'name' },
  { title: '手机号', dataIndex: 'phone', key: 'phone' },
  { title: '出生日期', dataIndex: 'birthday', key: 'birthday' },
  { title: '任教年级', dataIndex: 'gradeName', key: 'gradeName' },
  { title: '任教科目', dataIndex: 'subjectName', key: 'subjectName' },
  { title: '所属学段', dataIndex: 'section', key: 'section' },
  {
    title: '是否为班主任',
    dataIndex: 'isLeader',
    key: 'isLeader',
    customRender: ({ text }) => (text === false ? '否' : '是'),
  },
  {
    title: '是否为校长',
    dataIndex: 'isPrincipal',
    key: 'isPrincipal',
    customRender: ({ text }) => (text === false ? '否' : '是'),
  },
  { title: '备注', dataIndex: 'remark', key: 'remark' },
];



// 表格列表
export const columnsMapType = {
  school: schoolColumns,
  student: studentColumns,
  teacher: teacherColumns,
  // eltern: parentColumns,
};
