<template>
  <div class="joinTaskPage">
    <ContHeader type="back" title="任务详情" />
    <ul class="menu">
      <li
        v-for="(item, index) in menuList"
        :key="item.value"
        @click="changeMenu(item)"
      >
        <div class="top-wrap">
          <div class="icon-wrap" :style="activeStyle(item)">
            <i :class="['icon', 'iconfont', item.icon]"></i>
          </div>
          <img
            v-if="index !== menuList.length - 1"
            class="right-wrap"
            src="@/assets/images/line-bar.png"
            alt="line-bar.png"
          />
        </div>
        <p class="ellipsis title">{{ item.title }}</p>
      </li>
    </ul>
    <!-- v-if="route.query.collectJobId" -->
    <div class="joinTaskInfoBody">
      <component
        :is="moduleList[state.activeKey]"
        :monitoringId="route.query.id"
      ></component>
    </div>
    <!-- <div class="joinTaskInfoBody" v-else>
      <div class="empty-wrap">
        <img src="@/assets/images/pic-xxcj.png" alt="pic-xxcj.png" />
        <div style="color: rgba(0, 0, 0, 0.65)">任务未开始</div>
      </div>
    </div> -->
  </div>
</template>

<script setup name="joinTask">
import { h } from 'vue';
import { theme } from 'ant-design-vue';
//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
const { token } = theme.useToken();
const route = useRoute();

// 这都是我参与下的节点
const menuList = [
  {
    icon: 'icon-xinxicaiji',
    title: '信息上报',
    value: 'infoSend',
  },
  {
    icon: 'icon-shujuchouyang',
    title: '抽样结果',
    value: 'sampleResult',
  },
  {
    icon: 'icon-faqiceshi',
    title: '参与测试',
    value: 'participationText',
  },
  {
    icon: 'icon-faqiwenjuan',
    title: '参与问卷',
    value: 'joinStartQuestion',
  },
  {
    icon: 'icon-zhuanjiapinggu',
    title: '专家评估',
    value: 'joinEvaluate',
  },
  {
    icon: 'icon-baogaochakan',
    title: '报告查看',
    value: 'joinReport',
  },
];

const state = reactive({
  activeKey: 'infoSend',
});

const activeStyle = computed(() => {
  return item => {
    if (item.value === state.activeKey) {
      return {
        color: '#ffffff',
        borderColor: token.value.colorPrimaryBg,
        background: token.value.colorPrimaryBorderHover,
      };
    } else {
      return {
        background: token.value.colorPrimaryBg,
        borderColor: '#ffffff',
        color: token.value.colorPrimaryBorderHover,
      };
    }
  };
});

const changeMenu = item => {
  state.activeKey = item.value;
  console.log(item);
};

const moduleList = {
  infoSend: defineAsyncComponent(() => import('./child/infoSend/index.vue')), // 信息上报
  sampleResult: defineAsyncComponent(
    () => import('./child/sampleResult/index.vue')
  ), // 抽样结果
  participationText: defineAsyncComponent(
    () => import('./child/participationText/index.vue')
  ), // 参与测试

  joinStartQuestion: defineAsyncComponent(
    () => import('./child/joinStartQuestion/index.vue')
  ), // 参与问卷

  joinEvaluate: defineAsyncComponent(
    () => import('./child/joinEvaluate/index.vue')
  ), //专家评估

  joinReport: defineAsyncComponent(
    () => import('./child/joinReport/index.vue')
  ), //报告查看
};
</script>

<style lang="less" scoped>
.joinTaskPage {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.menu {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 15px 231px 16px;

  .top-wrap {
    display: flex;
    align-items: center;
  }

  .icon-wrap {
    box-sizing: border-box;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    cursor: pointer;
    border-radius: 50%;
    border: 4px solid #b8eccd;
    background: #4dd082;

    .icon {
      position: absolute;
      font-size: 40px;
      z-index: 2;
    }
  }

  .title {
    font-weight: 400;
    font-size: 12px;
    color: #595959;
    padding-top: 7px;
    cursor: pointer;
  }
}

.joinTaskInfoBody {
  height: 100%;
  overflow: hidden;
}

.empty-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 10%;
}
</style>
