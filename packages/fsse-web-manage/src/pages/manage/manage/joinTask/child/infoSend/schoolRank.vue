<!-- 校级 -->
<template>
  <div class="joinTaskInfoCont">
    <div class="infoLeft">
      <ul>
        <li
          v-for="(item, index) in compList"
          :key="item.id"
          @click="changeMenu(item)"
        >
          <p class="index">{{ index + 1 }}</p>
          <p class="title" :style="typeStyle(item)">{{ item.name }}</p>
        </li>
      </ul>
    </div>
    <div class="infoRight">
      <!-- 使用 KeepAlive 包裹动态组件以缓存状态 -->
      <KeepAlive>
        <component
          :is="importCompList[state.activateItem]"
          @changeActivateItem="changeActivateItem"
        ></component>
      </KeepAlive>
    </div>
  </div>
</template>

<script setup name="schoolRank">
import { onMounted } from 'vue';
import { theme } from 'ant-design-vue';
//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
const { token } = theme.useToken();
const state = ref({ activateItem: 'SchoolUp' });

const compList = [
  {
    name: '学校信息上报',
    id: '1',
    value: 'SchoolUp',
  },
  {
    name: '学生信息上报',
    id: '2',
    value: 'StudentUp',
  },
  // {
  //   name: '家长信息上报',
  //   id: '3',
  //   value: 'ParentsUp',
  // },
  {
    name: '教师信息上报',
    id: '4',
    value: 'TeacherUp',
  },
  {
    name: '监测室信息上报',
    id: '5',
    value: 'ClassroomUp',
  },
  {
    name: '监测员信息上报',
    id: '6',
    value: 'ExaminerUp',
  },
];

const importCompList = {
  SchoolUp: defineAsyncComponent(() => import('./module/schoolUp.vue')), // 学校信息上报
  StudentUp: defineAsyncComponent(() => import('./module/studentUp.vue')), // 学生信息上报
  // ParentsUp: defineAsyncComponent(() => import('./module/parentsUp.vue')), // 家长信息上报
  TeacherUp: defineAsyncComponent(() => import('./module/teacherUp.vue')), // 教师信息上报
  ClassroomUp: defineAsyncComponent(() => import('./module/classroomUp.vue')), // 监测室信息上报
  ExaminerUp: defineAsyncComponent(() => import('./module/examinerUp.vue')), // 监测员信息上报
};

const typeStyle = computed(() => {
  return (item) => {
    if (item.value === state.value.activateItem) {
      return {
        background: token.value.colorPrimaryBg,
        color: token.value.colorPrimary,
      };
    } else {
      return {
        background: '#fff',
        color: '#262626',
      };
    }
  };
});

function changeMenu(item) {
  state.value.activateItem = item.value;
}

const changeActivateItem = (val) => {
  state.value.activateItem = val;
};

onMounted(() => {
  console.log('onMounted3213');
});
</script>

<style lang="less" scoped>
.joinTaskInfoCont {
  display: flex;
  border-top: 1px solid #ecedf1;
  height: 100%;
  .infoLeft {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 165px;
    border-right: 1px solid #ecedf1;
    height: 100%;
    ul {
      display: flex;
      flex-direction: column;
      padding: 16px 8px 0 10px;
      flex: 1;
      overflow-y: auto;

      li {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        cursor: pointer;
      }

      .index {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        background: #ffffff;
        border: 1px solid var(--primary-color);
        border-radius: 20px;
        font-weight: 500;
        font-size: 14px;
        color: var(--primary-color);
      }

      .title {
        box-sizing: border-box;
        flex: 1;
        margin-left: 10px;
        border-radius: 4px;
        line-height: 32px;
        padding: 0 8px;
        font-weight: 400;
        font-size: 14px;
        color: #262626;
        &:hover {
          color: var(--primary-color) !important;
        }
      }
    }
  }
  .infoRight {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding: 16px;
    flex: 1;
    overflow-y: auto;
  }
}
</style>
