<template>
  <div class="downtownRankResultPage">
    <div class="resultLeft">
      <ul>
        <li
          v-for="(item, index) in compList"
          :key="item.id"
          @click="changeMenu(item)"
        >
          <p class="index">{{ index + 1 }}</p>
          <p class="title" :style="typeStyle(item)">{{ item.name }}</p>
        </li>
      </ul>
    </div>
    <div class="resultRight">
      <KeepAlive>
      <component :is="moduleList[state.activateItem]"></component>
    </KeepAlive>
    </div>
  </div>
</template>

<script setup name="downtownRank">
import { theme } from 'ant-design-vue';
//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
const { token } = theme.useToken();
const moduleList = {
  County: defineAsyncComponent(() => import('./module/county.vue')), // 区/县
  School: defineAsyncComponent(() => import('./module/school.vue')), // 学校
  Pupil: defineAsyncComponent(() => import('./module/pupil.vue')), // 学生
  // Patriarch: defineAsyncComponent(() => import('./module/patriarch.vue')), // 家长
  Teacher: defineAsyncComponent(() => import('./module/teacher.vue')), // 教师
};

const state = ref({ activateItem: 'County' });

const compList = [
  {
    name: '样本区/县',
    id: '2',
    value: 'County',
  },
  {
    name: '样本学校',
    id: '3',
    value: 'School',
  },
  {
    name: '样本学生',
    id: '4',
    value: 'Pupil',
  },
  {
    name: '样本教师',
    id: '5',
    value: 'Teacher',
  },
  // {
  //   name: '样本家长',
  //   id: '6',
  //   value: 'Patriarch',
  // },
];

const typeStyle = computed(() => {
  return (item) => {
    if (item.value === state.value.activateItem) {
      return {
        background: token.value.colorPrimaryBg,
        color: token.value.colorPrimary,
      };
    } else {
      return {
        background: '#fff',
        color: '#262626',
      };
    }
  };
});

const changeMenu = (item) => {
  state.value.activateItem = item.value;
};
</script>

<style lang="less" scoped>
.downtownRankResultPage {
  display: flex;
  border-top: 1px solid #ecedf1;
  height: 100%;
}

.resultLeft {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 165px;
  border-right: 1px solid #ecedf1;
  height: 100%;
  ul {
    display: flex;
    flex-direction: column;
    padding: 16px 8px 0 10px;
    flex: 1;
    overflow-y: auto;

    li {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      cursor: pointer;
    }

    .index {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      background: #ffffff;
      border: 1px solid var(--primary-color);
      border-radius: 20px;
      font-weight: 500;
      font-size: 14px;
      color: var(--primary-color);
    }

    .title {
      box-sizing: border-box;
      flex: 1;
      margin-left: 10px;
      border-radius: 4px;
      line-height: 32px;
      padding: 0 8px;
      font-weight: 400;
      font-size: 14px;
      color: #262626;
      &:hover {
        color: var(--primary-color) !important;
      }
    }
  }
}

.resultRight {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}
</style>
