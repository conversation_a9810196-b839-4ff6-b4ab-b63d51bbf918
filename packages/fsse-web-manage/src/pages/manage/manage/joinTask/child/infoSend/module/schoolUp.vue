<template>
  <div class="schoolUpMain">
    <div class="schoolUpPage">
      <div
        class="signboard"
        :style="{
          color: getStatusObj(state.reportInfo.collectStatus).color,
          background: getStatusObj(state.reportInfo.collectStatus).background,
        }"
      >
        {{ getStatusObj(state.reportInfo.collectStatus).text }}
      </div>
      <div class="formBox">
        <a-form
          ref="schooInfoFormRef"
          :model="schooInfoForm"
          name="schooInfoForm"
          class="schooInfoForm"
          :labelCol="{ span: 5, offset: 0 }"
        >
          <!-- 这三个是固定的 -->
          <a-form-item mb-20 name="name">
            <template #label>
              <div>
                <span class="redStar"> * </span>
                学校名称
              </div>
            </template>

            <a-input
              v-model:value="state.reportInfo.name"
              placeholder="请输入"
            />
          </a-form-item>
          <a-form-item mb-20 name="shortName">
            <template #label>
              <div>
                <span class="redStar"> * </span>
                学校简称
              </div>
            </template>
            <a-input
              v-model:value="state.reportInfo.shortName"
              placeholder="请输入"
            />
          </a-form-item>
          <a-form-item mb-20 name="code">
            <template #label>
              <div>
                <span class="redStar"> * </span>
                学校代码
              </div>
            </template>
            <a-input
              v-model:value="state.reportInfo.code"
              placeholder="请输入"
            />
          </a-form-item>

          <a-form-item mb-20 name="type">
            <template #label>
              <div>
                <span class="redStar"> * </span>
                类型
              </div>
            </template>
            <a-select
              v-model:value="state.reportInfo.type"
              :options="schoolTypeOptions"
              placeholder="请选择"
              allowClear
            ></a-select>
          </a-form-item>

          <!-- 指标这里通通全是根据接口动态生成的 -->
          <div v-for="item in state.formList" :key="item.id">
            <a-form-item mb-20 :name="item.indicatorId">
              <template #label>
                <div>
                  <span class="redStar"> * </span>
                  {{ item.name }}
                </div>
              </template>

              <!-- 输入框 -->
              <a-input
                v-if="item.indicatorRuleType === 'numerical'"
                v-model:value="schooInfoForm[item.indicatorId]"
                placeholder="请输入"
                allowClear
              />

              <!-- 选择器 -->
              <a-select
                v-else-if="item.indicatorRuleType === 'options'"
                v-model:value="schooInfoForm[item.indicatorId]"
                :options="computedIndicatorRuleValues(item.indicatorRuleValues)"
                placeholder="请选择"
                allowClear
              ></a-select>
            </a-form-item>
          </div>
        </a-form>
      </div>
      <div class="infoBox">
        <div class="infoBoxCon">
          <div>
            上报人：<span>{{ state.reportInfo.reportUserName || '-' }}</span>
          </div>
          <div>
            上报时间：<span>{{ state.reportInfo.reportTime || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="footBox">
      <a-button size="large" :block="true" type="primary" @click="nextStep"
        >下一步</a-button
      >
    </div>
  </div>
</template>

<script setup name="SchoolUp">
import { theme } from 'ant-design-vue';
//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
const { token } = theme.useToken();
const route = useRoute();
// 响应式数据
const state = ref({
  reportInfo: {
    reportTime: '-',
    reportUserName: '-',
  },
  formList: [],
});

function getStatusObj(key) {
  const statusObj = {
    0: {
      text: '未上报',
      color: '#595959',
      background: '#e0e0e0',
    },
    1: {
      text: '已上报',
      color: '#ffffff',
      background: '#00B781',
    },
    null: {
      text: '未上报',
      color: '#595959',
      background: '#e0e0e0',
    },
    default: {
      text: '-',
      color: '',
    },
  };
  return statusObj[key] || statusObj.default;
}

const schoolTypeOptions = [
  { label: '幼儿园', value: 1 },
  { label: '小学', value: 2 },
  { label: '中学', value: 3 },
  { label: '完全中学', value: 4 },
  { label: '九年一贯制学校', value: 5 },
  { label: '十二年一贯制学校', value: 6 },
  { label: '中心校', value: 7 },
  { label: '高中', value: 8 },
];
const schooInfoForm = ref({
  name: '',
});

const emit = defineEmits(['changeActivateItem']);

const nextStep = async () => {
  try {
    // 拼成后端需要的格式
    const indicators = Object.keys(schooInfoForm.value).map(key => ({
      indicatorId: parseInt(key),
      indicatorRuleValue: schooInfoForm.value[key],
    }));
    const res = await http.post(`/manage/collect/job/jobReport`, {
      collectJobId: route.query.collectJobId,
      indicators: indicators,
      schoolReport: {
        code: state.value.reportInfo.code,
        type: state.value.reportInfo.type,
        shortName: state.value.reportInfo.shortName,
      },
    });
    console.log(res);

    YMessage.success('操作成功');
    emit('changeActivateItem', 'StudentUp');
  } catch (e) {
    console.warn(e);
  }
};

// 计算选项数据
const computedIndicatorRuleValues = computed(() => arr => {
  return arr.map(item => ({ label: item, value: item }));
});

const getCityReportedInfo = async () => {
  try {
    const res = await http.post(
      `/manage/collect/job/listCollectJobIndicatorDetails`,
      {
        collectJobId: route.query.collectJobId,
        indicatorType: 'school',
      }
    );

    state.value.formList = res.data;
  } catch (e) {
    console.warn(e);
  }
};

const getJobReport = async () => {
  try {
    const res = await http.post(`/manage/collect/job/getJobReport`, {
      collectJobId: route.query.collectJobId,
    });

    // 一些基础信息
    state.value.reportInfo = res.data;

    // 指标反显
    schooInfoForm.value = res.data.indicators.reduce((result, item) => {
      result[item.indicatorId] = item.indicatorRuleValue;
      return result;
    }, {});

    // 基本信息反显
    state.value.reportInfo.code = res.data.schoolReport?.code || null;
    state.value.reportInfo.type = res.data.schoolReport?.type || null;
    state.value.reportInfo.shortName = res.data.schoolReport?.shortName || null;
  } catch (e) {
    console.warn(e);
  }
};

onMounted(() => {
  getJobReport();
  getCityReportedInfo();
});
</script>

<style lang="less" scoped>
.schoolUpMain {
  width: 700px;
  margin: 0 auto;
}
.schoolUpPage {
  position: relative;
  background: #f1f5f7;
  border-radius: 4px;
  padding-top: 40px;
  .signboard {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 0;
    top: 0;
    width: 72px;
    height: 32px;
    background: #e0e0e0;
    border-radius: 0px 4px 0px 11px;
    font-weight: 400;
    font-size: 14px;
    color: #595959;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }
}

.formBox {
  padding-right: 85px;
}

.infoBox {
  padding-left: 20px;
  padding-right: 20px;

  .infoBoxCon {
    border-top: 1px dashed #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 24px;
    padding-bottom: 24px;
  }
}

.footBox {
  padding-top: 32px;
  height: 44px;
}

.redStar {
  color: #ff4d4f;
}
</style>
