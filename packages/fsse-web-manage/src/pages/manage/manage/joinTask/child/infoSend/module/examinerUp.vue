<template>
  <searchForm
    v-model:formState="query"
    :formList="formList"
    @submit="searchBtn"
    @reset="resetBtn"
  />
  <div class="feat-btn-group">
    <a-button @click="importBtn"> 导入 </a-button>
    <a-button @click="exportBtn"> 导出 </a-button>
    <a-button
      danger
      :disabled="!state.selectedRowKeys.length"
      @click="deleteItems"
    >
      删除
    </a-button>
  </div>
  <ETable
    :minH="400"
    hash="examinerUpTable"
    :loading="page.loading"
    :columns="columns"
    :dataSource="computedList"
    :total="page.total"
    @paginationChange="paginationChange"
    :current="query.pageNo"
    :row-selection="{
      selectedRowKeys: state.selectedRowKeys,
      onChange: onSelectChange,
    }"
  >
  </ETable>

  <div class="footBox">
    <a-button
      ghost
      style="width: 244px"
      size="large"
      :block="true"
      type="primary"
      @click="backStep"
      >上一步</a-button
    >
    <a-button
      style="width: 244px"
      size="large"
      :block="true"
      type="primary"
      @click="nextStep"
      >一键上报</a-button
    >
  </div>
  <Import ref="ImportRef" @handleOk="resetBtn"></Import>
</template>

<script setup name="ExaminerUp">
import { createVNode } from 'vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
const emit = defineEmits(['changeActivateItem']);
const route = useRoute();
const ImportRef = ref(null);

import { Modal } from 'ant-design-vue';

const mainStore = useStore();
// let orgId = mainStore?.userInfo?.orgId || '';

let { query, page, getList, reset, paginationChange, updateByDelete } = useList(
  '/manage/collect/job/room/person/page',
  {
    collectJobId: route.query.collectJobId,
    // orgId: orgId,
  }
);
getList();
const state = ref({
  selectedRowKeys: [],
});

const formList = [
  {
    type: 'input',
    value: 'name',
    label: '监测员姓名',
  },
];

const person_type = {
  1: '监测点主任',
  2: '监测点副主任',
  3: '信息员',
  4: '安保人员',
  5: '司时员',
  6: '计算机技术人员',
  7: '主监测员',
  8: '副监测员',
  9: '学校负责人',
  default: '',
};
function getPersonType(key) {
  return person_type[key] || person_type.default;
}

// const columns = ref([
//   { title: '监测员姓名', dataIndex: 'name', key: 'name' },
//   { title: '联系方式', dataIndex: 'phone', key: 'phone' },
//   {
//     title: '人员类型',
//     dataIndex: 'type',
//     key: 'type',
//     customRender: ({ text }) => getPersonType(text),
//   },
//   { title: '监测年级', dataIndex: 'grade', key: 'grade' },
// ]);


const columns = ref([
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '手机号', dataIndex: 'phone', key: 'phone' },
  {
    title: '人员类型',
    dataIndex: 'type',
    key: 'type',
    customRender: ({ text }) => getPersonType(text),
  },
  { title: '监测年级', dataIndex: 'grade', key: 'grade' },
]);

const backStep = () => {
  console.log('上一步');
  emit('changeActivateItem', 'ClassroomUp');
};

// 一键上报
const nextStep = async () => {
  try {
    await http.post(`/manage/collect/job/schoolJobReport`, {
      collectJobId: route.query.collectJobId,
    });
    YMessage.success('上报成功');
  } catch (e) {
    console.warn(e);
  }
};

const onSelectChange = (selectedRowKeys) => {
  state.value.selectedRowKeys = selectedRowKeys;
};

// 获取指标的动态表头
const getColumns = async () => {
  try {
    const res = await http.post('/manage/collect/job/listCollectJobIndicator', {
      collectJobId: route.query.collectJobId,
      indicatorType: 'monitoring_member',
    });

    // 过滤出 checked 字段为 true的
    const checkedArr = res.data.filter((item) => item.checked === true);
    const columnsArr = checkedArr.map((item) => {
      return {
        title: item.name,
        dataIndex: item.id,
        key: item.id,
      };
    });

    console.log(res);

    columns.value = [...columns.value, ...columnsArr];
  } catch (e) {
    console.warn(e);
  }
};

// 查询
const searchBtn = () => {
  getList({
    collectJobId: route.query.collectJobId,
    // orgId: orgId,
  });
};

// 重置
const resetBtn = () => {
  reset({
    collectJobId: route.query.collectJobId,
    // orgId: orgId,
  });
};

const deleteItems = () => {
  Modal.confirm({
    title: '删除',
    icon: createVNode(ExclamationCircleFilled),
    content: `是否确认删除？`,
    okText: '确 定',
    cancelText: '取 消',
    onCancel() {},
    async onOk() {
      await http.post(`/manage/collect/job/room/person/delete`, {
        ids: state.value.selectedRowKeys,
      });
      YMessage.success('操作成功');
      state.value.selectedRowKeys = [];
      updateByDelete(state.value.selectedRowKeys.length);
    },
  });
};

const importBtn = () => {
  ImportRef.value.show({
    staticFile: false, // 需要动态下载文件
    importUrl: '/manage/collect/job/import', // 导入链接
    exprotUrl: '/manage/collect/job/room/person/export',
    importParams: {
      collectJobId: route.query.collectJobId,
      stepId: 14,
    }, // 导入参数
    exprots: [
      {
        fileName: '监测员信息上报模板', // 文件名
        downloadText: '监测员信息上报模板', // 按钮显示名称
        exprotUrl: '/manage/collect/job/room/person/export', // 导出
        params: {
          collectJobId: route.query.collectJobId,
          template: true,
        },
      },
    ],
  });
};

// 导出
const exportBtn = () => {
  try {
    http.download(
      `/manage/collect/job/room/person/export`,
      {
        collectJobId: route.query.collectJobId,
        template: false,
        name: query.name,
      },
      '监测员信息上报'
    );
  } catch (e) {
    console.warn(e);
  }
};

const computedList = computed(() => {
  // 拼接表格数据
  let resultArr = page.list.reduce((result, item) => {
    if (item.indicators && item.indicators.length) {
      let newItem = { ...item };
      item.indicators.forEach((indicator) => {
        newItem[indicator.indicatorId] = indicator.indicatorRuleValue;
      });
      result.push(newItem);
    } else {
      result.push(item);
    }
    return result;
  }, []);

  return resultArr;
});

onMounted(() => {
  getColumns();
});
</script>

<style lang="less" scoped>
.feat-btn-group {
  display: flex;
  justify-content: flex-end;
  margin: 16px 0;
}

.footBox {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 16px;
}
</style>
