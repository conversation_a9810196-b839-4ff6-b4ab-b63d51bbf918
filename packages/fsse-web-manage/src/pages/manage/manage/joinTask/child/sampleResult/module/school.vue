<!-- 样本学校 -->
<template>
  <div>
    <div pb16>
      <searchForm
        v-model:formState="query"
        :formList="formList"
        @submit="searchBtn"
        @reset="resetBtn"
      >
        <template #sheng>
          <a-select
            label="省"
            :fieldNames="{
              label: 'name',
              value: 'id',
            }"
            v-model:value="query.sheng"
            :options="state.shengList"
            placeholder="请选择"
            allowClear
            @change="cityChange"
          ></a-select>
        </template>
        <template #shi>
          <a-select
            label="市"
            :fieldNames="{
              label: 'name',
              value: 'id',
            }"
            v-model:value="query.shi"
            :options="state.shiList"
            placeholder="请选择"
            allowClear
            @change="cityChangeone"
          ></a-select>
        </template>
        <template #qu>
          <a-select
            label="区/县"
            :fieldNames="{
              label: 'name',
              value: 'id',
            }"
            v-model:value="query.qu"
            :options="state.quList"
            placeholder="请选择"
            allowClear
          ></a-select>
        </template>
      </searchForm>
    </div>
    <div
      style="
        font-weight: 400;
        font-size: 14px;
        color: #595959;
        padding-bottom: 12px;
      "
    >
      <span>抽样人：{{ state.info.school.samplingMemberName || '-' }}</span>
      <span ml-32>抽样时间：{{ state.info.school.createTime || '-' }}</span>
    </div>
    <ETable
      hash="downtown-table"
      :loading="page.loading"
      :columns="columns"
      :dataSource="computedList"
      :total="page.total"
      @paginationChange="paginationChange"
      :current="query.pageNo"
    >
    </ETable>
  </div>
</template>

<script setup name="downtown">
const route = useRoute();
const mainStore = useStore();
let orgId = mainStore?.userInfo?.orgId || '';
const formList = ref([
  {
    type: 'slot',
    value: 'sheng',
    label: '省',
  },
  {
    type: 'slot',
    value: 'shi',
    label: '市',
  },
  {
    type: 'slot',
    value: 'qu',
    label: '区/县',
  },
  {
    type: 'input',
    value: 'name',
    label: '学校名称',
  },
]);

const columns = ref([
  { title: '地市名称', dataIndex: 'cityName', key: 'cityName' },
  { title: '区/县名称', dataIndex: 'districtName', key: 'districtName' },
  { title: '学校名称', dataIndex: 'name', key: 'name' },
  { title: '学校简称', dataIndex: 'shortName', key: 'shortName' },
]);
const state = ref({
  cityList: [],
  countyList: [],
  fixedObj: {
    // orgId:orgId,
    collectJobId: route.query.collectJobId,
    samplingJobId: '',
  },
  info: {
    school: {
      samplingMemberName: '',
      createTime: '',
    },
  },
  shengList: [],
  shiList: [],
  quList: [],
});
let { query, page, getList, reset, paginationChange } = useList(
  '/manage/collect/job/school/page',
  state.value.fixedObj
);

const computedList = computed(() => {
  // 拼接表格数据
  let resultArr = page.list.reduce((result, item) => {
    if (item.indicators && item.indicators.length) {
      let newItem = { ...item };
      item.indicators.forEach((indicator) => {
        newItem[indicator.indicatorId] = indicator.indicatorRuleValue;
      });
      result.push(newItem);
    } else {
      result.push(item);
    }
    return result;
  }, []);

  return resultArr;
});

const searchBtn = () => {
  getList({
    ...state.value.fixedObj,
    areaId: query.qu || query.shi || query.sheng || '',
  });
};
const resetBtn = () => {
  reset({ ...state.value.fixedObj, areaId: null });
  state.value.shiList = [];
  state.value.quList = [];
};

// 获取指标的动态表头
const getColumns = async () => {
  try {
    const res = await http.post('/manage/collect/job/listCollectJobIndicator', {
      collectJobId: route.query.collectJobId,
      indicatorType: 'school',
    });

    // 过滤出 checked 字段为 true的
    const checkedArr = res.data.filter((item) => item.checked === true);
    const columnsArr = checkedArr.map((item) => {
      return {
        title: item.name,
        dataIndex: item.id,
        key: item.id,
      };
    });
    columns.value = [...columns.value, ...columnsArr];
  } catch (e) {
    console.warn(e);
  }
};

const getsamplingJobId = async () => {
  try {
    const res = await http.post(`/manage/sampling/job/getDetails`, {
      monitoringId: route.query.monitoringId,
    });
    // 获取抽样任务id
    state.value.fixedObj.samplingJobId = res.data?.id;
    // 获取抽样人 时间
    if (res.data?.samplingJobDetails) {
      res.data.samplingJobDetails.forEach(
        (item) => (state.value.info[item.samplingDataType] = item)
      );
    }
    if (res.data?.id) {
      await getList(state.value.fixedObj);
    }
  } catch (e) {
    console.warn(e);
  }
};

const getAreaList = async (params = { pid: 0, type: 1 }) => {
  const { data } = await http.post(`/manage/area/list`, params);
  if (params.type === 1) {
    state.value.shengList = data || [];
  } else if (params.type === 2) {
    state.value.shiList = data || [];
  } else if (params.type === 3) {
    state.value.quList = data || [];
  }
};

const cityChange = (value) => {
  if (value) {
    getAreaList({ pid: value, type: 2 });
  } else {
    state.value.shiList = [];
    state.value.quList = [];
    query.sheng = null;
    query.shi = null;
    query.qu = null;
  }
};

const cityChangeone = (value) => {
  if (value) {
    getAreaList({ pid: value, type: 3 });
  } else {
    state.value.quList = [];
    query.qu = null;
  }
};

onMounted(() => {
  getColumns();
  getsamplingJobId();
  getAreaList();
});
</script>

<style lang="less" scoped></style>
