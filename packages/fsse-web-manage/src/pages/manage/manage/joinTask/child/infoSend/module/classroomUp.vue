<template>
  <searchForm
    v-model:formState="query"
    :formList="formList"
    @submit="searchBtn"
    @reset="resetBtn"
  />
  <div class="feat-btn-group">
    <a-button @click="importBtn"> 导入 </a-button>
    <a-button @click="exportBtn"> 导出 </a-button>
    <a-button
      danger
      :disabled="!state.selectedRowKeys.length"
      @click="deleteItems"
    >
      删除
    </a-button>
  </div>
  <ETable
    :minH="400"
    hash="classroomUpTable"
    :loading="page.loading"
    :columns="columns"
    :dataSource="computedList"
    :total="page.total"
    @paginationChange="paginationChange"
    :current="query.pageNo"
    :row-selection="{
      selectedRowKeys: state.selectedRowKeys,
      onChange: onSelectChange,
    }"
  >
    <template #headerCell-layout>
      <span style="cursor: pointer" @click="handeSeatLayout">
        座位布局图 <question-circle-outlined class="iconColor" />
      </span>
    </template>

    <template #layout="{ record }">
      <a-tooltip>
        <template #title>
          <img
            :src="layoutImages[record.layout - 1]"
            alt="配置信息"
            style="width: 207px; height: 279px"
          />
        </template>
        <a-button type="text">样式{{ record.layout }}</a-button>
      </a-tooltip>
    </template>
    <template #row="{ record }">
      {{ `${record.row}*${record.cell}` }}
    </template>
    <template #configuration="{ record }">
      <a-button
        type="link"
        class="btn-link-color"
        @click="handerComputerConf(record)"
        >查看</a-button
      >
    </template>
    <template #provideHeadphone="{ record }">
      {{ record.provideHeadphone ? '是' : '否' }}
    </template>
  </ETable>

  <div class="footBox">
    <a-button
      ghost
      style="width: 244px"
      size="large"
      :block="true"
      type="primary"
      @click="backStep"
      >上一步</a-button
    >
    <a-button
      style="width: 244px"
      size="large"
      :block="true"
      type="primary"
      @click="nextStep"
      >下一步</a-button
    >
  </div>

  <Import ref="ImportRef" @handleOk="resetBtn"></Import>
  <!-- 座位布局图 -->
  <SeatLayoutModal ref="seatLayoutModalRef" />
  <!--  电脑配置信息 -->
  <ComputerConfModal
    ref="computerConfModalRef"
    :computerConfData="state.computerConfData"
  />
</template>

<script setup name="ClassroomUp">
import { createVNode } from 'vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import SeatLayoutModal from './seatLayoutModal.vue';
import ComputerConfModal from './computerConfModal.vue';
import { Modal } from 'ant-design-vue';
import seat1 from '@/assets/images/seat1.png';
import seat2 from '@/assets/images/seat2.png';
import seat3 from '@/assets/images/seat3.png';
const emit = defineEmits(['changeActivateItem']);
const route = useRoute();

const ImportRef = ref(null);
const computerConfModalRef = ref(null);
const layoutImages = shallowRef([seat1, seat2, seat3]);

const mainStore = useStore();
// let orgId = mainStore?.userInfo?.orgId || '';
// let areaId = mainStore?.userInfo?.fsseOrg?.areaId || '';

let { query, page, getList, reset, paginationChange, updateByDelete } = useList(
  '/manage/collect/job/room/page',
  {
    collectJobId: route.query.collectJobId,
    // orgId: orgId,
  }
);
getList();

const seatLayoutModalRef = ref(null);
const state = ref({
  selectedRowKeys: [],
});

const formList = [
  {
    type: 'input',
    value: 'name',
    label: '监测教室',
  },
];

// const columns = ref([
//   { title: '监测教室', dataIndex: 'name', key: 'name' },
//   { title: '计算机台数', dataIndex: 'computerNumber', key: 'computerNumber' },
//   {
//     title: '是否配备耳机',
//     dataIndex: 'provideHeadphone',
//     key: 'provideHeadphone',
//   },
//   { title: '电脑配置信息', dataIndex: 'configuration', key: 'configuration' },
//   { title: '座位布局图', dataIndex: 'layout', key: 'layout' },

//   { title: '行*列', dataIndex: 'row', key: 'row' },
// ]);

const columns = ref([
  { title: '监测教室', dataIndex: 'name', key: 'name' },
  { title: '计算机台数', dataIndex: 'computerNumber', key: 'computerNumber' },
]);

const handeSeatLayout = () => {
  seatLayoutModalRef.value.show();
};
const backStep = () => {
  console.log('上一步');
  emit('changeActivateItem', 'TeacherUp');
};

const nextStep = () => {
  emit('changeActivateItem', 'ExaminerUp');
};

const onSelectChange = (selectedRowKeys) => {
  state.value.selectedRowKeys = selectedRowKeys;
};

// 获取指标的动态表头
const getColumns = async () => {
  try {
    const res = await http.post('/manage/collect/job/listCollectJobIndicator', {
      collectJobId: route.query.collectJobId,
      indicatorType: 'monitoring_room',
    });

    // 过滤出 checked 字段为 true的
    const checkedArr = res.data.filter((item) => item.checked === true);
    const columnsArr = checkedArr.map((item) => {
      return {
        title: item.name,
        dataIndex: item.id,
        key: item.id,
      };
    });

    console.log(res);

    columns.value = [...columns.value, ...columnsArr];
  } catch (e) {
    console.warn(e);
  }
};

// 查询
const searchBtn = () => {
  getList({
    collectJobId: route.query.collectJobId,
    // orgId: orgId,
  });
};

// 重置
const resetBtn = () => {
  reset({
    collectJobId: route.query.collectJobId,
    // orgId: orgId,
  });
};

const deleteItems = () => {
  Modal.confirm({
    title: '删除',
    icon: createVNode(ExclamationCircleFilled),
    content: `是否确认删除？`,
    okText: '确 定',
    cancelText: '取 消',
    onCancel() {},
    async onOk() {
      await http.post(`/manage/collect/job/room/delete`, {
        ids: state.value.selectedRowKeys,
      });
      YMessage.success('操作成功');
      state.value.selectedRowKeys = [];
      updateByDelete(state.value.selectedRowKeys.length);
    },
  });
};

const importBtn = () => {
  ImportRef.value.show({
    staticFile: false, // 需要动态下载文件
    importUrl: '/manage/collect/job/import', // 导入链接
    exprotUrl: '/manage/collect/job/room/export',
    importParams: {
      collectJobId: route.query.collectJobId,
      stepId: 11,
    }, // 导入参数
    exprots: [
      {
        fileName: '监测室信息上报模板', // 文件名
        downloadText: '监测室信息上报模板', // 按钮显示名称
        exprotUrl: '/manage/collect/job/room/export', // 导出
        params: {
          collectJobId: route.query.collectJobId,
          template: true,
        },
      },
    ],
  });
};

// 导出
const exportBtn = () => {
  try {
    http.download(
      `/manage/collect/job/room/export`,
      {
        collectJobId: route.query.collectJobId,
        template: false,
        name: query.name,
        // areaId: areaId,
      },
      '监测室信息上报'
    );
  } catch (e) {
    console.warn(e);
  }
};

// 查看电脑配置信息
const handerComputerConf = (item) => {
  computerConfModalRef.value.computerConfData = toRaw(item);
  computerConfModalRef.value.showModal();
};

const computedList = computed(() => {
  // 拼接表格数据
  let resultArr = page.list.reduce((result, item) => {
    if (item.indicators && item.indicators.length) {
      let newItem = { ...item };
      item.indicators.forEach((indicator) => {
        newItem[indicator.indicatorId] = indicator.indicatorRuleValue;
      });
      result.push(newItem);
    } else {
      result.push(item);
    }
    return result;
  }, []);

  return resultArr;
});

onMounted(() => {
  getColumns();
});
</script>

<style lang="less" scoped>
.feat-btn-group {
  display: flex;
  justify-content: flex-end;
  margin: 16px 0;
}

.footBox {
  display: flex;
  justify-content: center;
  align-items: center;
}

.iconColor {
  color: var(--primary-color);
}
</style>
