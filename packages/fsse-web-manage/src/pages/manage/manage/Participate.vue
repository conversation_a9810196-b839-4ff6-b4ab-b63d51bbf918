<!-- 监测管理-我参与的主页面 -->
<template>
  <div class="participate">
    <div pb24>
      <searchForm
        v-model:formState="query"
        :formList="formList"
        @submit="searchBtn"
        @reset="resetBtn"
      />
    </div>
    <a-spin :spinning="page.loading">
      <div class="cardList">
        <div class="card-item-box" v-for="item in page.list" :key="item.id">
          <div class="cardItem" @click="jumpToTask(item)">
            <div class="id">ID:{{ item.id }}</div>
            <div
              class="status"
              :style="{
                backgroundColor: getStatusObj(item.monitoringStatus).color,
              }"
            >
              {{ getStatusObj(item.monitoringStatus).text }}
            </div>

            <div class="leftBox">
              <div class="imgBox"><img src="@/assets/images/pic-mr.png" /></div>
              <div class="titleBox">
                <div class="titlecon">{{ item.monitoringName }}</div>
                <div class="infoBox">
                  <span
                    style="width: 120px; flex-shrink: 0"
                    class="ellipsis year"
                    >监测年份：{{ item.monitoringYear }}年</span
                  >
                  <span
                    style="width: 180px; flex-shrink: 0"
                    class="ellipsis level"
                    >监测年级：{{
                      computedGrades(item.monitoringGrades || [])
                    }}</span
                  >
                  <span
                    style="width: 230px; flex-shrink: 0"
                    class="ellipsis time"
                    >起止时间：{{ item.monitoringStartTime }} -
                    {{ item.monitoringEndTime }}
                  </span>

                  <span class="ellipsis user"
                    >负责人：{{
                      computedMembers(item.monitoringMembers || [])
                    }}</span
                  >
                </div>
              </div>
            </div>
            <div class="rightBox">
              <div class="btn" @click.stop="openCheck(item)">
                <i class="iconfont icon-bggl-ck"></i> 查看
              </div>

              <div class="btn" @click.stop="jumpToTask(item)">
                <i class="iconfont icon-bggl-scbg"></i> 任务详情
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>

    <div
      style="margin: 16px 16px 0 0; text-align: right"
      v-if="page.list && page.list.length"
    >
      <Pagination
        :total="page.total"
        @paginationChange="paginationChange"
        :current="query.pageNo"
      ></Pagination>
    </div>
    <div v-else>
      <div
        :style="{ height: 'calc(100vh - 230px)' }"
        flex
        flex-col
        items-center
        justify-center
      >
        <img
          width="180"
          height="180"
          src="@/assets/images/empty.png"
          alt="暂无数据"
        />
        <div style="color: rgba(0, 0, 0, 0.65); font-size: 14px">暂无数据</div>
      </div>
    </div>

    <Check ref="checkRef" />
  </div>
</template>

<script setup>
import { theme } from 'ant-design-vue';
//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
const { token } = theme.useToken();

import Check from './Check.vue';
const router = useRouter();
let { query, page, getList, reset, paginationChange } = useList(
  '/manage/monitoring/page',
  { queryType: 2 }
);

getList();

function getStatusObj(key) {
  const statusObj = {
    0: {
      text: '未开始',
      color: '#D4D4D4',
    },
    1: {
      text: '进行中',
      color: token.value.colorPrimary,
    },
    2: {
      text: '已结束',
      color: '#D4D4D4',
    },
    default: {
      text: '-',
      color: '',
    },
  };
  return statusObj[key] || statusObj.default;
}

const computedGrades = computed(() => arr => {
  return arr.map(item => item.schoolGradeName).join('、');
});

const computedMembers = computed(() => arr => {
  return arr.map(item => item.name).join('、');
});

const formList = ref([
  {
    type: 'datePicker',
    attrs: {
      picker: 'year',
      format: 'YYYY年',
      valueFormat: 'YYYY',
    },
    value: 'monitoringYear',
    label: '监测年份',
  },
  {
    type: 'select',
    value: 'schoolGrade',
    label: '监测年级',
    attrs: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
    list: [],
  },
  {
    type: 'select',
    value: 'monitoringStatus',
    label: '进行状态',
    list: [
      { label: '未开始', value: 0 },
      { label: '进行中', value: 1 },
      { label: '已结束', value: 2 },
    ],
  },
]);

const checkRef = ref(null);

// 打开查看
const openCheck = item => {
  checkRef.value.showModal(item);
};

// 先获取有没有开启任务 开启了再跳转进任务详情
// 调接口获取采集任务id
const jumpToTask = async item => {
  try {
    const res = await http.post(`/manage/collect/job/getDetails`, {
      monitoringId: item.id,
    });

    if (res.data) {
      router.push({
        path: '/manage/manage/joinTask',
        query: {
          id: item.id,
          monitoringId: item.id,
          collectJobId: res.data.id,
        },
      });
    } else {
      router.push({
        path: '/manage/manage/joinTask',
        query: {
          id: item.id,
          monitoringId: item.id,
        },
      });
    }
  } catch (e) {
    console.warn(e);
  }
};

const searchBtn = () => {
  getList({ queryType: 2 });
};
const resetBtn = () => {
  reset({ queryType: 2 });
};

const getlistSectionCourse = () => {
  http.post('/manage/monitoring/listSectionSubject').then(res => {
    const newArr = [];
    res.data.forEach(item => {
      newArr.push(...item.gradeInfos);
    });
    formList.value[1].list = newArr;
  });
};

onMounted(() => {
  getList();
  getlistSectionCourse();
});
</script>

<style lang="less" scoped>
.participate {
  padding: 20px;

  .cardList {
    .card-item-box {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      .checkbox {
        margin-right: 12px;
      }
    }
    .cardItem {
      position: relative;
      cursor: pointer;
      min-height: 84px;
      background: #ffffff;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      flex-wrap: wrap;
      padding: 16px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      justify-content: space-between;
      overflow: hidden;
      flex: 1;
      &:hover {
        background: #f7fffc;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
      }

      .id {
        position: absolute;
        top: 0;
        left: 0;
        height: 14px;
        background: #eaeaea;
        border-radius: 4px 0px 4px 0px;
        font-weight: 400;
        font-size: 10px;
        color: #595959;
        padding: 0 6px;
      }

      .status {
        position: absolute;
        top: 5px;
        right: -20px;
        font-weight: 500;
        font-size: 11px;
        color: #ffffff;
        height: 20px;
        line-height: 20px;
        width: 76px;
        text-align: center;
        transform: rotate(40deg);
      }

      .leftBox {
        display: flex;
        align-items: center;
        .imgBox {
          width: 50px;
          height: 50px;
          background-color: v-bind('token.colorPrimaryBg');
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            width: 37px;
            height: 39px;
          }
        }
        .titleBox {
          padding-left: 10px;
          .titlecon {
            font-weight: 500;
            font-size: 14px;
            color: #000000;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            padding-bottom: 10px;
          }
          .infoBox {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            font-weight: 400;
            font-size: 14px;
            color: #595959;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            .year,
            .level,
            .time,
            .user {
              display: inline-block;
              font-weight: 400;
              font-size: 14px;
              color: #595959;
              margin-right: 24px;
            }
          }
        }
      }
      .rightBox {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        .btn {
          padding-left: 20px;
          color: #262626;
          font-size: 14px;
          cursor: pointer;
          &:hover {
            color: var(--primary-color);
          }
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .rightBox {
    margin-top: 12px;
    flex: 1;
    justify-content: end;
  }
}
</style>
