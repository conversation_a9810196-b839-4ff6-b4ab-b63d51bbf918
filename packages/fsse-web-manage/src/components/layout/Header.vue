<template>
  <a-layout-header class="layout_header">
    <div class="header_left">
      <!-- 有定制logo就用定制的图片 ,没有就用默认的 -->
      <div v-if="mainStore.userInfo?.fsseOrg?.config?.logo">
        <a-avatar :size="32" :src="mainStore.userInfo.fsseOrg.config.logo" />
      </div>
      <svg-icon v-else name="icon-logo" size="32"></svg-icon>
      <p class="title" @click="goHome">FSSE监测系统</p>
    </div>
    <div class="header_right">
      <div class="orgName">{{ mainStore.userInfo?.fsseOrg?.name }}</div>
      <a-dropdown :arrow="true">
        <span class="header_right_item">
          <svg-icon name="icon-shuxian" size="16"></svg-icon>

          <span class="username" pl-4>{{ mainStore.userInfo?.name }}</span>
          <CaretDownFilled class="down-icon" />
        </span>
        <template #overlay>
          <a-menu>
            <a-menu-item @click="modifyPassword">
              <div class="menu-item-wrap">
                <i
                  class="iconfont icon-icon-mima-nor"
                  :style="{ fontSize: '16px' }"
                  mr-3
                  mt-1
                ></i>
                <span>修改密码</span>
              </div>
            </a-menu-item>
            <a-menu-item @click="signOut">
              <div class="menu-item-wrap">
                <i
                  class="iconfont icon-icon-tcxt1"
                  :style="{ fontSize: '13px' }"
                  ml-2
                ></i>
                <span> 退出登录</span>
              </div>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </a-layout-header>
  <ModifyPassword ref="modifyPasswordRef" />
</template>

<script setup>
import ModifyPassword from './components/ModifyPassword.vue';

const mainStore = useStore();

// *********************
// Hooks Function
// *********************

const modifyPasswordRef = ref(null);

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const modifyPassword = () => {
  modifyPasswordRef.value.showModel();
};

const signOut = async () => {
  try {
    await http.post('/manage/auth/logout');
    window.localStorage.clear();
    window.sessionStorage.clear();
    mainStore.clearUser();
    window.location.replace('/#/login');
  } catch (error) {
    YMessage.error(error.message);
  }
};

const goHome = () => {
  window.location.replace('/');
};
</script>

<style lang="less" scoped>
.layout_header {
  background: var(--primary-color);
  padding: 0 24px;
  height: 48px;
  line-height: 48px;
  display: flex;
  justify-content: space-between;

  .header_left {
    display: flex;
    align-items: center;
    .logo {
      width: 32px;
      height: 32px;
    }

    .title {
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      margin-left: 12px;
    }
  }
  .header_right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .orgName {
      font-size: 14px;
      color: #ffffff;
      padding-right: 4px;
    }
    .header_right_l {
      display: flex;
      align-items: center;
      & > span {
        color: #2c2c2c;
        padding-right: 16px;
        margin-right: 16px;
        font-size: 14px;
        cursor: pointer;
        line-height: 18px;
        border-right: 1px solid #999999;
        &:hover {
          color: var(--primary-color);
        }
      }
    }

    .header_right_item {
      cursor: pointer;
      display: flex;
      align-items: center;
    }
    .header_right_item:hover {
      color: #19be8d;
    }
    .iconfont {
      font-size: 18px;
    }

    .username {
      font-weight: 400;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      margin: 0 8px 0 0;
    }
    .down-icon {
      font-size: 17px;
      color: #fff;
    }
  }

  .menu-item-wrap {
    display: flex;
    align-items: center;
  }
}
</style>
